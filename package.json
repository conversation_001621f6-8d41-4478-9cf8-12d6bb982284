{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@emailjs/nodejs": "^5.0.2", "@iconify/vue": "^5.0.0", "@nuxt/content": "^3.5.1", "@nuxtjs/robots": "^5.2.10", "@nuxtjs/sitemap": "^7.2.10", "@nuxtjs/tailwindcss": "^6.11.4", "@pinia/nuxt": "^0.11.0", "@vueuse/motion": "^2.1.0", "gsap": "^3.13.0", "locomotive-scroll": "^4.1.4", "nuxt": "^3.13.2", "pinia": "^3.0.2", "three": "^0.162.0"}}