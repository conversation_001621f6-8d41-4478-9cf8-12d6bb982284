---
title: Tarne<PERSON>
description: Inspired by <PERSON><PERSON><PERSON>, I built a versatile Unity deck system for any
  card game. This Unity-based digital card asset led to creating Tarneeb
coverImage: /images/projects/tarneeb.png
colors:
  primary: "#b60751"
  secondary: "#ff85af"
  accent: "#6BD8FA"
  background: "#222222"
techStack:
  - name: C#
    icon: devicon:csharp
  - name: Unity
    icon: bi:unity
  - name: ASP.NET
    icon: devicon:dot-net
  - name: SignalR
    icon: simple-icons:dotnet
  - name: Google Cloud
    icon: logos:google-cloud
  - name: Linux
    icon: mdi:linux
  - name: GitHub
    icon: mdi:github
  - name: Trello
    icon: devicon:trello
tags:
  - type: Individual
    icon: mdi:user
roles:
  - Full-Stack Developer
  - Game Designer
info:
  table:
    - label: Duration
      value: 4 months
    - label: Platform
      value: Android, Windows
  buttons:
    - text: Download for Android
      url: https://www.dropbox.com/scl/fi/e5bmt8v49kmvf8zyw9r41/Tarneeb.apk?rlkey=nbzwhtsta3cpw0nkopjdtcjvd&dl=0
    - text: View rule book
      url: https://www.dropbox.com/scl/fi/s9elm5j66cfzui3s2qrcq/Tarneeb-Rulebook.pdf?rlkey=2mrjxco0qnq0bue5q53nve8q4&dl=0
    - text: View Source
      url: https://github.com/FadiNahhas/Tarneeb-Mobile
seo:
  title: Tarneeb | Fadi Nahhas
  description: Inspired by Jawaker, I built a versatile Unity deck system for any
    card game. This Unity-based digital card asset led to creating Tarneeb
navigation:
  title: Tarneeb | Fadi Nahhas
---

# Tarneeb

Inspired by Jawaker’s engaging card game platform, I developed a versatile deck system in Unity to serve as a foundation for any card game. This project, a digital card game built in Unity, focused on creating a modular and scalable system to handle diverse mechanics like shuffling, dealing, and card interactions.

The deck system was designed for flexibility, allowing easy integration into various game projects. After completing it, I applied it to develop a digital version of Tarneeb, a popular Middle Eastern trick-taking card game. This involved implementing game logic, player interactions, and a polished interface, showcasing the system’s adaptability.

## Gameplay

### Multiplayer (Old UI)

::youtube
---
aspect-ratio: 16:9
id: 9fO8LslwwZs
title: Tarneeb - Multiplayer Gameplay
---
::

### Singleplayer (New UI)

::youtube
---
aspect-ratio: 16:9
id: W5W1CoDCwaw
title: Tarneeb - Singleplayer Gameplay
---
::

## Key Features

- **Realtime Multiplayer**: Utilizes SignalR for seamless turn-based multiplayer, syncing game states across devices with low latency for Tarneeb
- **Intelligent AI Opponents**: Sophisticated AI with a memory system tracks game history and teammate actions for strategic decisions, supporting offline play and online seat-filling
- **Online Game Browser (Legacy)**: Facilitates matchmaking by listing active games via SignalR
- **Online and Offline Modes**: Supports mobile-optimized offline mode and online mode using SignalR’s messaging

## Online Mode

The online mode was developed using SignalR and deployed on a Google Cloud Linux VM. It uses a messaging system to communicate between the client and the server. This framework made sense because the game is turn-based and does not require constant communication between the client and the server.\:brThe message system allowed for the same event-based communication method to be used to update the view on each client.\:brThe online mode was excluded from the latest build because the server cannot be kept online.

## Offline Mode

The offline mode went through multiple iterations. Initially, an enum based state machine was used, and later versions used a combination of functional programming and the event channel pattern using Unity's ScriptableObjects.\:brEventually the offline mode was rebuilt from the ground up using a class-based state machine and a global event system to communicate with the game view manager.\:brThe PC version of the project has been discontinued because most play-testers preferred to play on mobile. This allowed me to focus on the mobile version and redesign the entire UI.
