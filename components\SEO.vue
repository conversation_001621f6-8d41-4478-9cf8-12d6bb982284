<template>
  <!-- This is an invisible component that handles SEO metadata -->
</template>

<script setup>
import { useHead } from '#app';
import { computed } from 'vue';

// Props for the SEO component
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: 'Software Engineer specializing in game development and web applications. Building creative digital experiences with modern technologies.'
  },
  image: {
    type: String,
    default: '/images/og-image.jpg'
  },
  url: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'website'
  }
});

// Compute the full title with site name
const fullTitle = computed(() => {
  if (props.title) {
    return `${props.title} | Fadi <PERSON>`;
  }
  return 'Fadi Nahhas';
});

// Compute the canonical URL
const canonicalUrl = computed(() => {
  if (props.url) {
    return props.url.startsWith('http') ? props.url : `https://fadinahhas.com${props.url}`;
  }

  // If we're in the browser, use the current URL
  if (typeof window !== 'undefined') {
    return window.location.href;
  }

  return 'https://fadinahhas.com';
});

// Set the head metadata
useHead({
  title: fullTitle,
  meta: [
    { name: 'description', content: props.description },

    // Open Graph / Facebook
    { property: 'og:type', content: props.type },
    { property: 'og:title', content: fullTitle.value },
    { property: 'og:description', content: props.description },
    { property: 'og:image', content: props.image },
    { property: 'og:url', content: canonicalUrl.value },

    // Twitter
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: fullTitle.value },
    { name: 'twitter:description', content: props.description },
    { name: 'twitter:image', content: props.image },
    { name: 'twitter:url', content: canonicalUrl.value }
  ],
  link: [
    { rel: 'canonical', href: canonicalUrl.value }
  ]
});
</script>
