import { defineStore } from 'pinia';
import { useRoute } from 'vue-router';
import { watch } from 'vue';

// Define the theme store
export const useThemeStore = defineStore('theme', {
  state: () => {
    // Try to load theme preference from localStorage
    let savedIsGameDev = true; // Default to game dev

    // Only access localStorage in browser environment
    if (typeof window !== 'undefined') {
      try {
        const savedTheme = localStorage.getItem('themePreference');
        if (savedTheme !== null) {
          savedIsGameDev = savedTheme === 'game';
        }
      } catch (error) {
        console.error('Error loading theme preference from localStorage:', error);
      }
    }

    return {
      // Use saved theme preference or default to game dev (green)
      isGameDev: savedIsGameDev,

      // Flag to track if we're on a project page
      isProjectPage: false,

      // Project-specific theme class (if on a project page)
      projectThemeClass: null,

      // SSR-specific properties for project pages
      ssrPrimaryColor: null,
      ssrSecondaryColor: null,

      // Flag to prevent theme flashing during page transitions
      isTransitioning: false,

      // Store previous theme state for transitions
      previousThemeState: {
        isGameDev: savedIsGameDev,
        isProjectPage: false,
        projectThemeClass: null
      }
    };
  },

  getters: {
    // Get the current theme class
    themeClass: (state) => {
      // If we're in a transition, use the previous theme state to prevent flashing
      if (state.isTransitioning) {
        // If previous state was a project page with a theme class
        if (state.previousThemeState.isProjectPage && state.previousThemeState.projectThemeClass) {
          // If projectThemeClass is a string, return it directly
          if (typeof state.previousThemeState.projectThemeClass === 'string') {
            return state.previousThemeState.projectThemeClass;
          }
          // If it's an object (with colors), return an empty string (no class needed)
          return '';
        }

        // Otherwise use the previous theme based on isGameDev
        return state.previousThemeState.isGameDev ? '' : 'theme-web';
      }

      // Normal behavior when not transitioning
      // If on a project page, use project theme class
      if (state.isProjectPage && state.projectThemeClass) {
        // If projectThemeClass is a string, return it directly
        if (typeof state.projectThemeClass === 'string') {
          return state.projectThemeClass;
        }
        // If it's an object (with colors), return an empty string (no class needed)
        // as we'll use CSS variables instead
        return '';
      }

      // Otherwise use the theme based on isGameDev
      return state.isGameDev ? '' : 'theme-web';
    },

    // Get theme colors based on current theme
    themeColors: (state) => {
      // If we're in a transition, use the previous theme state to prevent flashing
      if (state.isTransitioning) {
        // If previous state was a project page with a theme class object
        if (state.previousThemeState.isProjectPage &&
            typeof state.previousThemeState.projectThemeClass === 'object' &&
            state.previousThemeState.projectThemeClass?.primary) {
          return {
            primary: state.previousThemeState.projectThemeClass.primary,
            secondary: state.previousThemeState.projectThemeClass.secondary || '#008F11',
            accent: state.previousThemeState.projectThemeClass.accent || '#003B00',
            background: state.previousThemeState.projectThemeClass.background || '#121212'
          };
        }

        // Otherwise use the previous theme colors based on isGameDev
        return state.previousThemeState.isGameDev
          ? {
              primary: '#00FF41',    // Matrix green
              secondary: '#008F11',  // Darker green
              accent: '#003B00',     // Very dark green
              background: '#121212'  // Dark background
            }
          : {
              primary: '#00A3FF',    // Bright blue
              secondary: '#0077B6',  // Medium blue
              accent: '#003D5B',     // Dark blue
              background: '#121212'  // Dark background
            };
      }

      // Normal behavior when not transitioning
      // If on a project page and we have a project theme class object with colors
      if (state.isProjectPage && typeof state.projectThemeClass === 'object' && state.projectThemeClass?.primary) {
        return {
          primary: state.projectThemeClass.primary,
          secondary: state.projectThemeClass.secondary || '#008F11',
          accent: state.projectThemeClass.accent || '#003B00',
          background: state.projectThemeClass.background || '#121212'
        };
      }

      // Default colors based on theme
      return state.isGameDev
        ? {
            primary: '#00FF41',    // Matrix green
            secondary: '#008F11',  // Darker green
            accent: '#003B00',     // Very dark green
            background: '#121212'  // Dark background
          }
        : {
            primary: '#00A3FF',    // Bright blue
            secondary: '#0077B6',  // Medium blue
            accent: '#003D5B',     // Dark blue
            background: '#121212'  // Dark background
          };
    }
  },

  actions: {
    // Start a theme transition - save current state
    startTransition() {
      // Save the current theme state
      this.previousThemeState = {
        isGameDev: this.isGameDev,
        isProjectPage: this.isProjectPage,
        projectThemeClass: this.projectThemeClass
      };

      // Set transitioning flag
      this.isTransitioning = true;
    },

    // End a theme transition
    endTransition() {
      this.isTransitioning = false;
    },

    // Save theme preference to localStorage
    saveThemePreference() {
      if (typeof window !== 'undefined') {
        try {
          const themeValue = this.isGameDev ? 'game' : 'web';
          localStorage.setItem('themePreference', themeValue);

          // Verify the save was successful
          const savedValue = localStorage.getItem('themePreference');
          if (savedValue !== themeValue) {
            console.warn('Theme preference verification failed. Expected:', themeValue, 'Got:', savedValue);
          }
        } catch (error) {
          console.error('Error saving theme preference to localStorage:', error);
        }
      }
    },

    // Toggle between game dev and web dev themes
    toggleDevMode() {
      this.isGameDev = !this.isGameDev;
      this.applyTheme();
      this.saveThemePreference();
    },

    // Set to game dev mode
    setGameDev() {
      this.isGameDev = true;
      this.applyTheme();
      this.saveThemePreference();
    },

    // Set to web dev mode
    setWebDev() {
      this.isGameDev = false;
      this.applyTheme();
      this.saveThemePreference();
    },

    // Apply theme class to HTML element with improved error handling
    applyTheme() {
      try {
        // Only run in browser environment
        if (typeof window === 'undefined') return;

        // Skip if on a project page and projectThemeClass is set
        // This prevents overriding project-specific themes while on project pages
        if (this.isProjectPage && this.projectThemeClass) return;

        if (document && document.documentElement) {
          try {
            // Remove all theme classes
            document.documentElement.classList.remove('theme-web');
          } catch (classError) {
            console.warn('Error removing theme classes:', classError);
          }

          try {
            // Add the current theme class
            if (!this.isGameDev) {
              document.documentElement.classList.add('theme-web');
            }
          } catch (classAddError) {
            console.warn('Error adding theme class:', classAddError);
          }

          // Update CSS variables based on the current theme
          try {
            const colors = this.themeColors;
            if (colors) {
              // Convert hex to RGB for rgba usage with improved error handling
              const hexToRgb = (hex) => {
                try {
                  if (!hex || typeof hex !== 'string') {
                    return '0, 255, 65'; // Default green
                  }

                  // Ensure hex is properly formatted
                  hex = hex.trim();
                  if (!hex.startsWith('#')) {
                    hex = '#' + hex;
                  }

                  // Handle both 3-digit and 6-digit hex
                  if (hex.length === 4) {
                    // Convert 3-digit hex to 6-digit
                    hex = '#' + hex[1] + hex[1] + hex[2] + hex[2] + hex[3] + hex[3];
                  }

                  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
                  return result
                    ? `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}`
                    : '0, 255, 65'; // Default green
                } catch (hexError) {
                  console.warn('Error parsing hex color, using default:', hexError);
                  return '0, 255, 65'; // Default green
                }
              };

              // Set primary color
              document.documentElement.style.setProperty('--primary-color', colors.primary);
              document.documentElement.style.setProperty('--primary-rgb', hexToRgb(colors.primary));

              // Set secondary color
              document.documentElement.style.setProperty('--secondary-color', colors.secondary);
              document.documentElement.style.setProperty('--secondary-rgb', hexToRgb(colors.secondary));

              // Set accent color
              document.documentElement.style.setProperty('--accent-color', colors.accent);
              document.documentElement.style.setProperty('--accent-rgb', hexToRgb(colors.accent));

              // Set background color
              document.documentElement.style.setProperty('--background-color', colors.background);
            }
          } catch (cssError) {
            console.error('Error setting CSS variables:', cssError);
          }
        }
      } catch (error) {
        console.error('Error applying theme:', error);

        // Last resort fallback - directly set CSS variables to defaults
        try {
          if (typeof window !== 'undefined' && document && document.documentElement) {
            // Set default game dev theme (green) or web dev theme (blue) based on isGameDev
            const primary = this.isGameDev ? '#00FF41' : '#00A3FF';
            const primaryRgb = this.isGameDev ? '0, 255, 65' : '0, 163, 255';
            const secondary = this.isGameDev ? '#008F11' : '#0077B6';
            const secondaryRgb = this.isGameDev ? '0, 143, 17' : '0, 119, 182';
            const accent = this.isGameDev ? '#003B00' : '#003D5B';
            const accentRgb = this.isGameDev ? '0, 59, 0' : '0, 61, 91';

            document.documentElement.style.setProperty('--primary-color', primary);
            document.documentElement.style.setProperty('--primary-rgb', primaryRgb);
            document.documentElement.style.setProperty('--secondary-color', secondary);
            document.documentElement.style.setProperty('--secondary-rgb', secondaryRgb);
            document.documentElement.style.setProperty('--accent-color', accent);
            document.documentElement.style.setProperty('--accent-rgb', accentRgb);
            document.documentElement.style.setProperty('--background-color', '#121212');

            // Set theme class
            document.documentElement.classList.remove('theme-web');
            if (!this.isGameDev) {
              document.documentElement.classList.add('theme-web');
            }
          }
        } catch (fallbackError) {
          console.error('Critical error: Failed to apply fallback theme:', fallbackError);
        }
      }
    },

    // Set a project-specific theme with improved error handling
    setProjectTheme(themeClass) {
      try {
        // Validate input
        if (!themeClass) {
          console.warn('setProjectTheme called with invalid theme data');
          return;
        }

        // First, ensure we have the latest theme preference from localStorage
        // This is crucial for handling direct page loads and refreshes on project pages
        if (typeof window !== 'undefined') {
          try {
            const savedTheme = localStorage.getItem('themePreference');
            if (savedTheme !== null) {
              // Update the state based on the saved preference
              this.isGameDev = savedTheme === 'game';

              // Also update the previous state to maintain consistency
              this.previousThemeState.isGameDev = savedTheme === 'game';
            }
          } catch (error) {
            console.error('Error loading theme preference in setProjectTheme:', error);
          }
        }

        // Store the current game/web preference before setting project theme
        // This helps preserve the preference when transitioning between pages
        const currentIsGameDev = this.isGameDev;

        this.isProjectPage = true;
        this.projectThemeClass = themeClass;

        // Preserve the game/web preference
        this.isGameDev = currentIsGameDev;

        // Also update the previous state to maintain consistency
        this.previousThemeState.isGameDev = currentIsGameDev;

        // For SSR, set the state variables but don't manipulate DOM
        if (typeof window === 'undefined') {
          // Set CSS variables for SSR
          if (typeof themeClass === 'object' && themeClass !== null) {
            // These will be used by the CSS variables in the stylesheets and by components during SSR
            this.ssrPrimaryColor = themeClass.primary || '#00FF41';
            this.ssrSecondaryColor = themeClass.secondary || themeClass.primary || '#008F11';
          }
          return;
        }

        // Save the theme preference to ensure it's preserved across refreshes
        this.saveThemePreference();

        if (document && document.documentElement) {
          try {
            // Remove all theme classes
            document.documentElement.classList.remove('theme-web');
          } catch (classError) {
            console.warn('Error removing theme classes:', classError);
          }

          // If themeClass is a string (CSS class name), add it to the HTML element
          if (typeof themeClass === 'string') {
            try {
              document.documentElement.classList.add(themeClass);
            } catch (classAddError) {
              console.warn('Error adding theme class:', classAddError);
            }
          }
          // If themeClass is an object with colors, update CSS variables
          else if (typeof themeClass === 'object' && themeClass !== null) {
            // Convert hex to RGB for rgba usage with improved error handling
            const hexToRgb = (hex) => {
              try {
                if (!hex || typeof hex !== 'string') {
                  return '0, 255, 65'; // Default green
                }

                // Ensure hex is properly formatted
                hex = hex.trim();
                if (!hex.startsWith('#')) {
                  hex = '#' + hex;
                }

                // Handle both 3-digit and 6-digit hex
                if (hex.length === 4) {
                  // Convert 3-digit hex to 6-digit
                  hex = '#' + hex[1] + hex[1] + hex[2] + hex[2] + hex[3] + hex[3];
                }

                const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
                return result
                  ? `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}`
                  : '0, 255, 65'; // Default green
              } catch (hexError) {
                console.warn('Error parsing hex color, using default:', hexError);
                return '0, 255, 65'; // Default green
              }
            };

            // Set CSS variables based on the theme colors
            try {
              if (themeClass.primary) {
                document.documentElement.style.setProperty('--primary-color', themeClass.primary);
                document.documentElement.style.setProperty('--primary-rgb', hexToRgb(themeClass.primary));
              }

              if (themeClass.secondary) {
                document.documentElement.style.setProperty('--secondary-color', themeClass.secondary);
                document.documentElement.style.setProperty('--secondary-rgb', hexToRgb(themeClass.secondary));
              }

              if (themeClass.accent) {
                document.documentElement.style.setProperty('--accent-color', themeClass.accent);
                document.documentElement.style.setProperty('--accent-rgb', hexToRgb(themeClass.accent));
              }

              if (themeClass.background) {
                document.documentElement.style.setProperty('--background-color', themeClass.background);
              }
            } catch (cssError) {
              console.error('Error setting CSS variables:', cssError);
            }
          }
        }
      } catch (error) {
        console.error('Error applying project theme:', error);

        // Fallback to default theme
        try {
          this.resetToDefaultTheme();
        } catch (resetError) {
          console.error('Failed to reset theme after error:', resetError);
        }
      }
    },

    // Reset to default theme while preserving game/web preference
    resetToDefaultTheme() {
      try {
        // First, ensure we have the latest theme preference from localStorage
        // This is crucial for handling direct page loads and refreshes
        if (typeof window !== 'undefined') {
          try {
            const savedTheme = localStorage.getItem('themePreference');
            if (savedTheme !== null) {
              // Update the state based on the saved preference
              this.isGameDev = savedTheme === 'game';

              // Also update the previous state to maintain consistency
              this.previousThemeState.isGameDev = savedTheme === 'game';
            }
          } catch (error) {
            console.error('Error loading theme preference in resetToDefaultTheme:', error);
          }
        }

        // Store the current game/web preference
        const currentIsGameDev = this.isGameDev;

        // Reset project-specific properties
        this.isProjectPage = false;
        this.projectThemeClass = null;

        // Clear any SSR-specific properties
        this.ssrPrimaryColor = null;
        this.ssrSecondaryColor = null;

        // Restore the game/web preference that was active before
        this.isGameDev = currentIsGameDev;

        // Update the previous state to maintain consistency
        this.previousThemeState.isGameDev = currentIsGameDev;
        this.previousThemeState.isProjectPage = false;
        this.previousThemeState.projectThemeClass = null;

        // Force immediate CSS variable reset in browser environment
        if (typeof window !== 'undefined' && document && document.documentElement) {
          // Set CSS variables based on the current theme preference
          const primary = currentIsGameDev ? '#00FF41' : '#00A3FF';
          const primaryRgb = currentIsGameDev ? '0, 255, 65' : '0, 163, 255';
          const secondary = currentIsGameDev ? '#008F11' : '#0077B6';
          const secondaryRgb = currentIsGameDev ? '0, 143, 17' : '0, 119, 182';
          const accent = currentIsGameDev ? '#003B00' : '#003D5B';
          const accentRgb = currentIsGameDev ? '0, 59, 0' : '0, 61, 91';

          // Apply CSS variables directly
          document.documentElement.style.setProperty('--primary-color', primary);
          document.documentElement.style.setProperty('--primary-rgb', primaryRgb);
          document.documentElement.style.setProperty('--secondary-color', secondary);
          document.documentElement.style.setProperty('--secondary-rgb', secondaryRgb);
          document.documentElement.style.setProperty('--accent-color', accent);
          document.documentElement.style.setProperty('--accent-rgb', accentRgb);
          document.documentElement.style.setProperty('--background-color', '#121212');

          // Update theme class
          document.documentElement.classList.remove('theme-web');
          if (!currentIsGameDev) {
            document.documentElement.classList.add('theme-web');
          }
        }

        // Apply the theme with preserved preference
        this.applyTheme();

        // Save the theme preference to ensure it's preserved across refreshes
        this.saveThemePreference();
      } catch (error) {
        console.error('Error resetting to default theme:', error);

        // Only run in browser environment
        if (typeof window === 'undefined') return;

        // Last resort fallback - directly set CSS variables to defaults
        try {
          if (document && document.documentElement) {
            // Get the current game/web preference for fallback
            const isGameDev = this.isGameDev;

            // Set theme based on the current preference
            if (isGameDev) {
              // Game dev theme (green)
              document.documentElement.style.setProperty('--primary-color', '#00FF41');
              document.documentElement.style.setProperty('--primary-rgb', '0, 255, 65');
              document.documentElement.style.setProperty('--secondary-color', '#008F11');
              document.documentElement.style.setProperty('--secondary-rgb', '0, 143, 17');
              document.documentElement.style.setProperty('--accent-color', '#003B00');
              document.documentElement.style.setProperty('--accent-rgb', '0, 59, 0');
            } else {
              // Web dev theme (blue)
              document.documentElement.style.setProperty('--primary-color', '#00A3FF');
              document.documentElement.style.setProperty('--primary-rgb', '0, 163, 255');
              document.documentElement.style.setProperty('--secondary-color', '#0077B6');
              document.documentElement.style.setProperty('--secondary-rgb', '0, 119, 182');
              document.documentElement.style.setProperty('--accent-color', '#003D5B');
              document.documentElement.style.setProperty('--accent-rgb', '0, 61, 91');
            }

            // Set background color (same for both themes)
            document.documentElement.style.setProperty('--background-color', '#121212');

            // Update theme class based on preference
            document.documentElement.classList.remove('theme-web');
            if (!isGameDev) {
              document.documentElement.classList.add('theme-web');
            }
          }
        } catch (fallbackError) {
          console.error('Critical error: Failed to apply fallback theme:', fallbackError);
        }
      }
    },

    // Initialize theme without route dependency
    initializeTheme() {
      try {
        // Only run browser-specific code in browser environment
        if (typeof window === 'undefined') return;

        // Get the current path from window.location
        const currentPath = window.location.pathname;

        // Check if we're on a project page
        const isProjectPage = (path) => {
          return path.startsWith('/projects/') && path !== '/projects/' && path !== '/projects';
        };

        // Set initial state based on current path
        const onProjectPage = isProjectPage(currentPath);

        // Only update if not already set (to avoid overriding SSR values)
        if (!this.isProjectPage) {
          this.isProjectPage = onProjectPage;
        }

        // Always load theme preference from localStorage first, regardless of page type
        try {
          const savedTheme = localStorage.getItem('themePreference');
          if (savedTheme !== null) {
            // Update the state based on the saved preference
            this.isGameDev = savedTheme === 'game';

            // Store this in previousThemeState too to ensure consistency during transitions
            this.previousThemeState.isGameDev = savedTheme === 'game';
          }
        } catch (error) {
          console.error('Error loading theme preference during initialization:', error);
        }

        // If we're on a project page, we'll let the project page set its theme
        // But we still need to ensure the game/web preference is preserved
        if (this.isProjectPage) {
          // Don't apply theme here as the project page will do it
          // But do save the preference to ensure it's stored
          this.saveThemePreference();
        } else {
          // For non-project pages, apply theme immediately
          // This ensures CSS variables are set properly from the start
          this.applyTheme();

          // Force a save to ensure the preference is stored
          this.saveThemePreference();
        }

        // Note: Project pages will call setProjectTheme themselves
      } catch (error) {
        console.error('Error initializing theme:', error);
      }
    }
  }
});
