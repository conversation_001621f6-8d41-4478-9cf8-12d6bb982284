<template>
  <a 
    :href="href" 
    :target="isExternal ? '_blank' : undefined" 
    :rel="isExternal ? 'noopener noreferrer' : undefined"
    class="text-current hover:opacity-80 transition-opacity duration-300 underline decoration-1 underline-offset-2"
    :style="{ color: 'var(--primary-color)' }"
  >
    <slot />
  </a>
</template>

<script setup>
const props = defineProps({
  href: {
    type: String,
    required: true
  }
});

// Check if the link is external
const isExternal = computed(() => {
  return props.href.startsWith('http') || props.href.startsWith('//');
});
</script>
