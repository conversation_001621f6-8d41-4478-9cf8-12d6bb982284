import emailjs from '@emailjs/nodejs';

export default defineEventHandler(async (event) => {
  try {
    // Get request body
    const body = await readBody(event);

    // Validate request body
    if (!body.name || !body.email || !body.subject || !body.message) {
      console.error('Invalid request body:', body);
      return createError({
        statusCode: 400,
        statusMessage: 'Missing required fields',
        data: 'Please provide name, email, subject, and message'
      });
    }

    // Access environment variables
    const emailjsPublicKey = process.env.EMAILJS_PUBLIC_KEY;
    const emailjsPrivateKey = process.env.EMAILJS_PRIVATE_KEY;
    const emailjsServiceId = process.env.EMAILJS_SERVICE_ID;
    const emailjsTemplateId = process.env.EMAILJS_TEMPLATE_ID;

    // Check credentials
    if (!emailjsPublicKey || !emailjsPrivateKey || !emailjsServiceId || !emailjsTemplateId) {
      console.error('Missing EmailJS credentials');
      return createError({
        statusCode: 500,
        statusMessage: 'Email service not configured properly',
        data: 'Missing EmailJS credentials'
      });
    }

    // Initialize EmailJS
    emailjs.init({ publicKey: emailjsPublicKey, privateKey: emailjsPrivateKey });

    // Prepare template parameters
    const templateParams = {
      from_name: body.name,
      email: body.email, // Matches template expectation
      subject: body.subject,
      message: body.message
    };

    // Send email
    const result = await emailjs.send(emailjsServiceId, emailjsTemplateId, templateParams);

    return { success: true, result };
  } catch (error) {
    console.error('Error sending email:', {
      message: error.message,
      text: error.text,
      response: error.response,
      stack: error.stack
    });
    return createError({
      statusCode: 500,
      statusMessage: 'Failed to send email',
      data: error.text || error.message || 'Unknown error'
    });
  }
});