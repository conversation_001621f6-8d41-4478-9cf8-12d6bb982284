/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./components/**/*.{js,vue,ts}",
    "./layouts/**/*.vue",
    "./pages/**/*.vue",
    "./plugins/**/*.{js,ts}",
    "./app.vue",
    "./error.vue",
    "./nuxt.config.{js,ts}",
    "./content/**/*.md", // Include content files for proper purging
  ],

  // Performance optimizations
  corePlugins: {
    // Disable unused core plugins for smaller bundle size
    preflight: true,
    container: false, // Not using container
    accessibility: true,
    pointerEvents: true,
    visibility: true,
    position: true,
    inset: true,
    isolation: false, // Rarely used
    zIndex: true,
    order: false, // Not using flexbox order
    gridColumn: true,
    gridColumnStart: true,
    gridColumnEnd: true,
    gridRow: true,
    gridRowStart: true,
    gridRowEnd: true,
    float: false, // Modern layout doesn't use float
    clear: false, // Modern layout doesn't use clear
    margin: true,
    boxSizing: true,
    display: true,
    aspectRatio: true,
    height: true,
    maxHeight: true,
    minHeight: true,
    width: true,
    minWidth: true,
    maxWidth: true,
    flex: true,
    flexShrink: true,
    flexGrow: true,
    flexBasis: true,
    tableLayout: false, // Not using tables
    borderCollapse: false, // Not using tables
    borderSpacing: false, // Not using tables
    transformOrigin: true,
    translate: true,
    rotate: true,
    skew: false, // Rarely used
    scale: true,
    transform: true,
    animation: true,
    cursor: true,
    touchAction: true,
    userSelect: true,
    resize: false, // Rarely used
    scrollSnapType: true,
    scrollSnapAlign: true,
    scrollSnapStop: false, // Rarely used
    scrollMargin: false, // Rarely used
    scrollPadding: false, // Rarely used
    listStylePosition: false, // Custom list styling
    listStyleType: false, // Custom list styling
    appearance: true,
    columns: false, // Not using CSS columns
    breakBefore: false, // Not using print styles
    breakInside: false, // Not using print styles
    breakAfter: false, // Not using print styles
    gridAutoColumns: false, // Not using auto grid
    gridAutoFlow: false, // Not using auto grid
    gridAutoRows: false, // Not using auto grid
    gridTemplateColumns: true,
    gridTemplateRows: true,
    flexDirection: true,
    flexWrap: true,
    placeContent: true,
    placeItems: true,
    alignContent: true,
    alignItems: true,
    justifyContent: true,
    justifyItems: true,
    gap: true,
    space: true,
    divideWidth: false, // Custom dividers
    divideColor: false, // Custom dividers
    divideStyle: false, // Custom dividers
    divideOpacity: false, // Custom dividers
    placeSelf: true,
    alignSelf: true,
    justifySelf: true,
    overflow: true,
    overscrollBehavior: true,
    scrollBehavior: true,
    textOverflow: true,
    whitespace: true,
    wordBreak: true,
    borderRadius: true,
    borderWidth: true,
    borderColor: true,
    borderStyle: true,
    borderOpacity: true,
    backgroundColor: true,
    backgroundOpacity: true,
    backgroundImage: true,
    gradientColorStops: true,
    backgroundSize: true,
    backgroundAttachment: false, // Rarely used
    backgroundClip: true,
    backgroundPosition: true,
    backgroundRepeat: true,
    backgroundOrigin: false, // Rarely used
    fill: true,
    stroke: true,
    strokeWidth: true,
    objectFit: true,
    objectPosition: true,
    padding: true,
    textAlign: true,
    textColor: true,
    textOpacity: true,
    textDecoration: true,
    textDecorationColor: true,
    textDecorationStyle: true,
    textDecorationThickness: true,
    textUnderlineOffset: true,
    textTransform: true,
    textIndent: false, // Rarely used
    verticalAlign: true,
    fontFamily: true,
    fontSize: true,
    fontWeight: true,
    fontVariantNumeric: false, // Rarely used
    letterSpacing: true,
    lineHeight: true,
    listStyleImage: false, // Custom list styling
    placeholderColor: true,
    placeholderOpacity: true,
    caretColor: true,
    accentColor: true,
    opacity: true,
    backgroundBlendMode: false, // Rarely used
    mixBlendMode: false, // Rarely used
    boxShadow: true,
    boxShadowColor: true,
    outline: true,
    outlineStyle: true,
    outlineWidth: true,
    outlineColor: true,
    outlineOffset: true,
    ringWidth: true,
    ringColor: true,
    ringOpacity: true,
    ringOffsetWidth: true,
    ringOffsetColor: true,
    blur: true,
    brightness: true,
    contrast: true,
    dropShadow: true,
    grayscale: true,
    hueRotate: true,
    invert: true,
    saturate: true,
    sepia: true,
    filter: true,
    backdropBlur: true,
    backdropBrightness: true,
    backdropContrast: true,
    backdropGrayscale: true,
    backdropHueRotate: true,
    backdropInvert: true,
    backdropOpacity: true,
    backdropSaturate: true,
    backdropSepia: true,
    backdropFilter: true,
    transitionProperty: true,
    transitionDelay: true,
    transitionDuration: true,
    transitionTimingFunction: true,
    willChange: true,
    content: true,
  },

  theme: {
    extend: {
      // Custom CSS variables for theme colors
      colors: {
        primary: 'rgb(var(--primary-rgb) / <alpha-value>)',
        secondary: 'rgb(var(--secondary-rgb) / <alpha-value>)',
        accent: 'rgb(var(--accent-rgb) / <alpha-value>)',
        background: 'var(--background-color)',
      },

      // Optimized screen breakpoints
      screens: {
        'sm': '640px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
        '2xl': '1536px',
      },

      // Performance-optimized animations
      animation: {
        'fade-in': 'fadeIn 0.5s ease-out',
        'slide-up': 'slideUp 0.6s ease-out',
        'scale-in': 'scaleIn 0.4s ease-out',
        'glow': 'glow 2s ease-in-out infinite alternate',
      },

      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.9)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        glow: {
          '0%': { textShadow: '0 0 5px rgb(var(--primary-rgb) / 0.5)' },
          '100%': { textShadow: '0 0 20px rgb(var(--primary-rgb) / 0.8)' },
        },
      },

      // Optimized spacing scale
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },

      // Typography optimizations
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'Consolas', 'monospace'],
      },

      // Glass effect utilities
      backdropBlur: {
        xs: '2px',
      },

      // Custom utilities for performance
      willChange: {
        'transform-opacity': 'transform, opacity',
      },
    },
  },

  // Production optimizations
  ...(process.env.NODE_ENV === 'production' && {
    // Enable JIT mode for production
    mode: 'jit',

    // Purge unused styles more aggressively
    purge: {
      enabled: true,
      content: [
        "./components/**/*.{js,vue,ts}",
        "./layouts/**/*.vue",
        "./pages/**/*.vue",
        "./plugins/**/*.{js,ts}",
        "./app.vue",
        "./error.vue",
        "./nuxt.config.{js,ts}",
        "./content/**/*.md",
      ],
      options: {
        safelist: [
          // Keep theme classes
          'theme-web',
          // Keep animation classes
          /^animate-/,
          // Keep dynamic classes that might be generated
          /^bg-/,
          /^text-/,
          /^border-/,
        ],
      },
    },
  }),

  plugins: [
    // Custom plugin for theme utilities
    function({ addUtilities, theme }) {
      const newUtilities = {
        '.glass': {
          'backdrop-filter': 'blur(10px)',
          'background-color': 'rgba(255, 255, 255, 0.1)',
          'border': '1px solid rgba(255, 255, 255, 0.2)',
        },
        '.glass-dark': {
          'backdrop-filter': 'blur(10px)',
          'background-color': 'rgba(0, 0, 0, 0.3)',
          'border': '1px solid rgba(255, 255, 255, 0.1)',
        },
        '.text-glow': {
          'text-shadow': '0 0 10px rgb(var(--primary-rgb) / 0.6)',
        },
        '.border-glow': {
          'box-shadow': '0 0 10px rgb(var(--primary-rgb) / 0.3)',
        },
      }

      addUtilities(newUtilities)
    },
  ],
}
