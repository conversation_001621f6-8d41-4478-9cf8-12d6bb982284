<template>
  <div class="flex items-center justify-center space-x-2 sm:space-x-3 md:space-x-4">
    <span :class="[!isActive ? 'font-bold active-label' : 'text-gray-400']"
          class="transition-colors duration-300 text-xs sm:text-sm md:text-base">
      {{ leftLabel }}
    </span>

    <button @click="toggle"
            class="relative w-12 h-6 sm:w-14 sm:h-7 md:w-16 md:h-8 rounded-full bg-gray-700 flex items-center p-1 cursor-pointer">
      <div ref="toggleHandle" class="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 rounded-full shadow-md transform transition-transform"
           :style="{
             transform: isActive ? `translateX(${toggleDistance}px)` : 'translateX(0)',
             backgroundColor: 'var(--primary-color)'
           }">
      </div>
    </button>

    <span :class="[isActive ? 'font-bold active-label' : 'text-gray-400']"
          class="transition-colors duration-300 text-xs sm:text-sm md:text-base">
      {{ rightLabel }}
    </span>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
import { gsap } from 'gsap';
import { useThemeStore } from '~/stores/theme';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  leftLabel: {
    type: String,
    default: 'Off'
  },
  rightLabel: {
    type: String,
    default: 'On'
  },
  initialValue: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue']);

const themeStore = useThemeStore();
const toggleHandle = ref(null);

// Initialize with the provided initialValue or fallback to the theme store value
const isActive = ref(typeof props.initialValue !== 'undefined' ? props.initialValue : themeStore.isGameDev);

// Computed property to determine toggle distance based on screen size
const toggleDistance = computed(() => {
  // Check if we're in a browser environment
  if (typeof window === 'undefined') return 32; // Default for SSR

  const width = window.innerWidth;
  if (width < 640) return 24; // Mobile
  if (width < 768) return 28; // Small tablets
  return 32; // Desktop
});

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  if (newValue !== isActive.value) {
    isActive.value = newValue;
    animateToggle();
  }
});

// Watch for theme changes
watch(() => themeStore.themeColors, () => {
  if (toggleHandle.value) {
    // Update toggle handle color when theme changes
    gsap.to(toggleHandle.value, {
      backgroundColor: themeStore.themeColors.primary,
      duration: 0.4
    });
  }
}, { deep: true });

const toggle = () => {
  isActive.value = !isActive.value;
  emit('update:modelValue', isActive.value);
  animateToggle();
};

const animateToggle = () => {
  gsap.to(toggleHandle.value, {
    x: isActive.value ? toggleDistance.value : 0,
    duration: 0.4,
    ease: 'elastic.out(1, 0.5)',
    backgroundColor: themeStore.themeColors.primary
  });
};

// Function to update toggle position
const updateTogglePosition = () => {
  if (toggleHandle.value) {
    toggleHandle.value.style.transform = isActive.value ? `translateX(${toggleDistance.value}px)` : 'translateX(0)';
  }
};

// Handle window resize
const handleResize = () => {
  updateTogglePosition();
};

onMounted(() => {
  try {
    // Ensure isActive is synced with the current theme state
    if (typeof props.modelValue !== 'undefined') {
      // If modelValue is provided, use it
      isActive.value = props.modelValue;
    } else if (typeof props.initialValue !== 'undefined') {
      // If initialValue is provided, use it
      isActive.value = props.initialValue;
    } else {
      // Otherwise, use the theme store value
      isActive.value = themeStore.isGameDev;
    }

    // Set initial position
    if (toggleHandle.value) {
      toggleHandle.value.style.transform = isActive.value ? `translateX(${toggleDistance.value}px)` : 'translateX(0)';
      toggleHandle.value.style.backgroundColor = themeStore.themeColors.primary;
    }

    // Add resize event listener
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', handleResize);
    }
  } catch (error) {
    console.error('Error initializing toggle switch:', error);
  }
});

// Clean up event listeners
onUnmounted(() => {
  if (typeof window !== 'undefined') {
    window.removeEventListener('resize', handleResize);
  }
});
</script>

<style scoped>
.active-label {
  color: var(--primary-color);
}
</style>
