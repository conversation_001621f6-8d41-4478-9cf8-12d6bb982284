---
title: Escapod
description: Your home is under attack by a monstrous alien! Guide the survivors
  to their designated Escapod to ensure their escape. Keep the line moving at
  all costs!
coverImage: /images/projects/escapod.png
colors:
  primary: "#24b3f4"
  secondary: "#88ea4a"
  accent: "#ff8d05"
  background: "#301B37"
techStack:
  - name: GameMaker
    icon: simple-icons:gamemaker
  - name: GitHub
    icon: mdi:github
  - name: Trello
    icon: devicon:trello
tags:
  - type: Group
    icon: mdi:account-group
  - type: Opera Game Jam
    icon: mdi:opera
roles:
  - Lead Programmer
  - Game Designer
info:
  table:
    - label: Duration
      value: 3 days
    - label: Platform
      value: Web, Mobile
  buttons:
    - text: Play Game
      url: https://gx.games/games/kk5imy/escapod/
---

# Escapod

Your home is under attack by a monstrous alien! Guide the survivors to their designated Escapod to ensure their escape. Keep the line moving at all costs!

## Game Concept

Escapod is a fast-paced management game where you must guide survivors to their color-coded escape pods before the alien catches them. The game was developed in just 3 days for the Opera Game Jam, with the theme "Keep the line moving."

## Gameplay

- **Matching Colors**: Each survivor has a designated escape pod color
- **Time Management**: Process survivors quickly before the alien's anger meter fills
- **Resource Allocation**: Decide which survivors to prioritize
- **Increasing Difficulty**: The game speeds up as you progress

::youtube{#F8EFlBL1duU aspect-ratio="16:9" title="Escapod Gameplay"}
::

## Development Process

As the lead programmer, I was responsible for:

1. Implementing the core gameplay mechanics
2. Creating the dynamic difficulty system
3. Developing the music system that intensifies as the anger meter fills
4. Integrating the art assets created by our team's artist

The game was built using GameMaker Studio 2 and exported to HTML5 for web play. We used a pixel art style to create a retro aesthetic that complemented the game's frantic pace.

## Technical Challenges

One of the main technical challenges was implementing the dynamic music system. The music needed to seamlessly transition between different intensity levels based on the alien's anger meter. This was achieved by:

- Creating multiple music layers with increasing intensity
- Implementing a crossfade system to blend between layers
- Synchronizing the music changes with visual cues

Another challenge was optimizing the game for web play, ensuring it ran smoothly even on lower-end devices. This required careful management of resources and efficient coding practices.
