<template>
  <div class="snap-section flex flex-col justify-center px-4 relative h-full">
    <div class="w-full max-w-lg md:max-w-2xl mx-auto">
      <div class="text-center">
        <h1
          v-motion
          :initial="{ opacity: 0 }"
          :enter="{ opacity: 1, transition: { duration: 600 } }"
          class="text-3xl md:text-5xl font-bold mb-6 md:mb-8"
        >
          About Me
        </h1>

        <GlassContainer
          v-motion
          :initial="{ opacity: 0 }"
          :enter="{ opacity: 1, transition: { duration: 600, delay: 300 } }"
          class="mb-8 md:mb-10 relative z-10"
        >
          <p class="text-base md:text-xl text-gray-300">
            I'm a passionate developer with expertise in both web development and game development.
            With a strong foundation in modern technologies, I create engaging digital experiences
            that combine creativity with technical excellence.
          </p>
        </GlassContainer>
      </div>
    </div>
  </div>
</template>

<script setup>
import GlassContainer from '../ui/GlassContainer.vue';

</script>

<style scoped>
.snap-section {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

</style>
