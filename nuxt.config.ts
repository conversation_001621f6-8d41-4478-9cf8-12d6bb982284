// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2024-04-03',
  // Disable SSR to prevent hydration issues with complex animations and dynamic content
  ssr: false,
  modules: [
    '@nuxt/content',
    '@nuxtjs/tailwindcss',
    '@vueuse/motion/nuxt',
    '@pinia/nuxt',
    '@nuxtjs/sitemap',
    '@nuxtjs/robots'
  ],
  // No runtime config needed for EmailJS as we access env vars directly
  content: {
    highlight: {
      theme: 'github-dark'
    },
    // Register components that can be used in markdown
    markdown: {
      remarkPlugins: ['remark-emoji'],
      // Add custom components that can be used in markdown
      tags: {
        p: 'MDParagraph',
        a: 'MDLink',
        img: 'MDImage',
        h1: 'MDH1',
        h2: 'MDH2',
        h3: 'MDH3',
        h4: 'MDH4',
        h5: 'MDH5',
        h6: 'MDH6',
        ul: 'MDList',
        ol: 'MDOrderedList',
        li: 'MDListItem',
        youtube: 'Youtube'
      },
      // Enable component usage in markdown
      components: {
        global: true,
        dirs: ['~/components', '~/components/content']
      }
    },
      preview: {
      api: 'https://api.nuxt.studio'
    }
  },
  app: {
    pageTransition: {
      name: 'page',
      mode: 'out-in'
    },
    head: {
      charset: 'utf-8',
      viewport: 'width=device-width, initial-scale=1',
      title: 'Fadi Nahhas',
      meta: [
        { name: 'description', content: 'Software Engineer specializing in game development and web applications. Building creative digital experiences with modern technologies.' },
        { name: 'format-detection', content: 'telephone=no' },
        { name: 'theme-color', content: '#121212' },

        // Open Graph / Facebook
        { property: 'og:type', content: 'website' },
        { property: 'og:title', content: 'Fadi Nahhas' },
        { property: 'og:description', content: 'Software Engineer specializing in game development and web applications. Building creative digital experiences with modern technologies.' },
        { property: 'og:image', content: '/images/og-image.jpg' },

        // Twitter
        { name: 'twitter:card', content: 'summary_large_image' },
        { name: 'twitter:title', content: 'Fadi Nahhas' },
        { name: 'twitter:description', content: 'Software Engineer specializing in game development and web applications. Building creative digital experiences with modern technologies.' },
        { name: 'twitter:image', content: '/images/og-image.jpg' }
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }
      ]
    }
  },
  experimental: {
    inlineRouteRules: false
  },
  // Auto-import components
  components: [
    { path: '~/components', pathPrefix: false },
    { path: '~/components/ui', pathPrefix: false },
    { path: '~/components/projects', pathPrefix: false },
    { path: '~/components/contact', pathPrefix: false }
  ],

  // Sitemap Configuration
  sitemap: {
    hostname: process.env.SITE_URL || 'https://fadinahhas.com',
    gzip: true,
    exclude: [
      '/admin/**'
    ],
    defaults: {
      changefreq: 'weekly',
      priority: 0.8,
      lastmod: new Date()
    }
  },

  // Robots Configuration
  robots: {
    UserAgent: '*',
    Allow: '/',
    Sitemap: (process.env.SITE_URL || 'https://fadinahhas.com') + '/sitemap.xml'
  }
})
