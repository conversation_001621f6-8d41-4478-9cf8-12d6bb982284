// Plugin to forcefully reset scroll position when navigating to project pages
export default defineNuxtPlugin((nuxtApp) => {
  // Track if we're currently handling a project page navigation
  let isHandlingProjectNavigation = false;

  // Before navigation starts
  nuxtApp.hook('page:start', () => {
    const route = useRoute();
    if (isProjectPage(route.path)) {
      isHandlingProjectNavigation = true;

      // Try to disable any smooth scrolling libraries
      disableScrollLibraries();
    }
  });

  // During navigation - this runs after the component is mounted but before it's visible
  nuxtApp.hook('page:transition:finish', () => {
    const route = useRoute();
    if (isProjectPage(route.path)) {
      // Force scroll reset during transition
      forceScrollToTop();
    }
  });

  // After navigation completes - the page is now visible
  nuxtApp.hook('page:finish', () => {
    const route = useRoute();
    if (isProjectPage(route.path)) {
      // Force scroll to top immediately
      forceScrollToTop();

      // Schedule multiple attempts with increasing delays
      const delays = [10, 50, 100, 200, 500, 1000];
      delays.forEach(delay => {
        setTimeout(() => {
          if (isProjectPage(route.path)) {
            forceScrollToTop();
          }
        }, delay);
      });

      // Reset the handling flag
      setTimeout(() => {
        isHandlingProjectNavigation = false;
      }, Math.max(...delays) + 100);
    }
  });

  // Helper function to check if a path is a project detail page
  function isProjectPage(path) {
    return path.startsWith('/projects/') && path !== '/projects/';
  }

  // Helper function to disable any scroll libraries that might interfere
  function disableScrollLibraries() {
    if (typeof window === 'undefined' || !document) return;

    try {
      // Try to access the Locomotive Scroll instance if it exists
      const nuxtApp = useNuxtApp();
      if (nuxtApp && nuxtApp.$locomotiveScroll && typeof nuxtApp.$locomotiveScroll.destroy === 'function') {
        nuxtApp.$locomotiveScroll.destroy();
      }

      // Safely access DOM elements
      if (document.documentElement) {
        document.documentElement.style.scrollBehavior = 'auto';
        document.documentElement.classList.remove('has-scroll-init', 'has-scroll-smooth');
      }

      if (document.body) {
        document.body.style.scrollBehavior = 'auto';
        document.body.classList.remove('has-scroll-init', 'has-scroll-smooth');
      }
    } catch (error) {
      // Silently ignore errors during hot reload
    }
  }

  // Helper function to force scroll to top using multiple methods
  function forceScrollToTop() {
    if (typeof window === 'undefined' || !document) return;

    try {
      // Method 1: Standard scrollTo with coordinates
      window.scrollTo(0, 0);

      // Method 2: ScrollTo with options
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: 'auto' // Use 'auto' instead of 'instant' for better compatibility
      });

      // Method 3: Set scrollTop directly on multiple elements
      if (document.documentElement) document.documentElement.scrollTop = 0;
      if (document.body) document.body.scrollTop = 0;

      // Method 4: Try to find the main content element and scroll it
      try {
        const main = document.querySelector('main');
        if (main) main.scrollTop = 0;
      } catch (mainError) {
        // Ignore main element errors
      }

      // Method 5: Try to find any scrollable containers and reset them
      try {
        document.querySelectorAll('.scroll-container, [data-scroll-container]').forEach(container => {
          if (container) container.scrollTop = 0;
        });
      } catch (containerError) {
        // Ignore container errors
      }

      // Method 6: Use history API to force a scroll reset
      try {
        if (history && history.scrollRestoration) {
          history.scrollRestoration = 'manual';
        }
      } catch (historyError) {
        // Ignore history API errors
      }
    } catch (error) {
      // Silently ignore errors during hot reload
    }
  }
});
