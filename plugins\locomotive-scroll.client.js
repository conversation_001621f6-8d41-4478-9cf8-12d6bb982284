// Optimized Locomotive Scroll plugin with memory leak prevention and performance improvements
import LocomotiveScroll from 'locomotive-scroll'
import 'locomotive-scroll/dist/locomotive-scroll.css'

export default defineNuxtPlugin((nuxtApp) => {
  // Only run on client-side
  if (process.server) return

  // Instance management for proper cleanup
  let currentInstance = null
  const eventListeners = new Map()
  const resizeObserver = new WeakMap()

  // Optimized wrapper around locomotive-scroll
  const locomotiveScroll = {
    // Initialize with performance optimizations
    init(options = {}) {
      try {
        // Cleanup existing instance first
        this.destroy()

        // Validate environment
        if (typeof window === 'undefined' || !window.document?.documentElement) {
          console.warn('Cannot initialize LocomotiveScroll: window or document not available')
          return null
        }

        // Validate element
        const el = options.el
        if (!el) {
          console.warn('Cannot initialize LocomotiveScroll: no element provided')
          return null
        }

        // Optimized default configuration
        const defaultOptions = {
          smooth: true,
          multiplier: 1,
          class: 'is-revealed',
          scrollbarContainer: false,
          scrollbarClass: 'c-scrollbar',
          scrollingClass: 'has-scroll-scrolling',
          draggingClass: 'has-scroll-dragging',
          smoothClass: 'has-scroll-smooth',
          initClass: 'has-scroll-init',
          getSpeed: false,
          getDirection: false,
          scrollFromAnywhere: false,
          inertia: 0.1,
          touchMultiplier: 2,
          resetNativeScroll: true,
          tablet: {
            smooth: false,
            direction: 'vertical',
            horizontalGesture: false
          },
          smartphone: {
            smooth: false,
            direction: 'vertical'
          }
        }

        // Create instance with optimized settings
        currentInstance = new LocomotiveScroll({
          ...defaultOptions,
          ...options
        })

        // Setup performance monitoring
        this._setupPerformanceMonitoring()

        return currentInstance
      } catch (error) {
        console.error('Failed to initialize LocomotiveScroll:', error)
        return null
      }
    },

    // Get the current instance
    getInstance() {
      return currentInstance
    },

    // Performance monitoring setup
    _setupPerformanceMonitoring() {
      if (!currentInstance || !process.dev) return

      let frameCount = 0
      let lastTime = performance.now()

      const monitor = () => {
        frameCount++
        const currentTime = performance.now()

        if (currentTime - lastTime >= 1000) {
          const fps = Math.round((frameCount * 1000) / (currentTime - lastTime))
          if (fps < 30) {
            console.warn(`LocomotiveScroll performance warning: ${fps} FPS`)
          }
          frameCount = 0
          lastTime = currentTime
        }

        if (currentInstance) {
          requestAnimationFrame(monitor)
        }
      }

      requestAnimationFrame(monitor)
    },

    // Optimized scroll to target
    scrollTo(target, options = {}) {
      try {
        if (!currentInstance) {
          console.warn('Cannot scroll: LocomotiveScroll not initialized')
          return
        }

        // Throttle scroll calls for better performance
        if (this._scrollTimeout) {
          clearTimeout(this._scrollTimeout)
        }

        this._scrollTimeout = setTimeout(() => {
          currentInstance.scrollTo(target, {
            duration: 1000,
            easing: [0.25, 0.0, 0.35, 1.0],
            ...options
          })
        }, 16) // ~60fps throttling
      } catch (error) {
        console.error('Failed to scroll with LocomotiveScroll:', error)
      }
    },

    // Debounced update for better performance
    update() {
      try {
        if (!currentInstance) {
          console.warn('Cannot update: LocomotiveScroll not initialized')
          return
        }

        // Debounce updates to prevent excessive calls
        if (this._updateTimeout) {
          clearTimeout(this._updateTimeout)
        }

        this._updateTimeout = setTimeout(() => {
          currentInstance.update()
        }, 100)
      } catch (error) {
        console.error('Failed to update LocomotiveScroll:', error)
      }
    },

    // Comprehensive cleanup
    destroy() {
      try {
        // Clear timeouts
        if (this._scrollTimeout) {
          clearTimeout(this._scrollTimeout)
          this._scrollTimeout = null
        }
        if (this._updateTimeout) {
          clearTimeout(this._updateTimeout)
          this._updateTimeout = null
        }

        // Remove all tracked event listeners
        eventListeners.forEach((listeners, element) => {
          listeners.forEach(({ event, handler }) => {
            element.removeEventListener(event, handler)
          })
        })
        eventListeners.clear()

        // Destroy instance
        if (currentInstance) {
          currentInstance.destroy()
          currentInstance = null
        }
      } catch (error) {
        console.error('Failed to destroy LocomotiveScroll:', error)
      }
    },

    // Enhanced event listener management
    on(event, callback) {
      try {
        if (!currentInstance) {
          console.warn('Cannot add event listener: LocomotiveScroll not initialized')
          return
        }

        currentInstance.on(event, callback)

        // Track for cleanup
        if (!eventListeners.has(currentInstance)) {
          eventListeners.set(currentInstance, [])
        }
        eventListeners.get(currentInstance).push({ event, callback })
      } catch (error) {
        console.error(`Failed to add ${event} event listener to LocomotiveScroll:`, error)
      }
    },

    // Enhanced event listener removal
    off(event, callback) {
      try {
        if (!currentInstance) return

        currentInstance.off(event, callback)

        // Remove from tracking
        const listeners = eventListeners.get(currentInstance)
        if (listeners) {
          const index = listeners.findIndex(l => l.event === event && l.callback === callback)
          if (index > -1) {
            listeners.splice(index, 1)
          }
        }
      } catch (error) {
        console.error(`Failed to remove ${event} event listener from LocomotiveScroll:`, error)
      }
    }
  }

  // Cleanup on page transitions
  nuxtApp.hook('page:transition:finish', () => {
    locomotiveScroll.destroy()
  })

  // Cleanup on app unmount
  nuxtApp.hook('app:beforeUnmount', () => {
    locomotiveScroll.destroy()
  })

  // Provide the optimized locomotive-scroll wrapper
  nuxtApp.provide('locomotiveScroll', locomotiveScroll)
})
