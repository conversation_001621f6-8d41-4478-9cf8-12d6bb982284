// Consolidated GSAP plugin following Nuxt best practices
import { gsap } from 'gsap';
// Import CSSPlugin explicitly
import { CSSPlugin } from 'gsap/CSSPlugin';

export default defineNuxtPlugin((nuxtApp) => {
  // Register CSSPlugin explicitly to ensure it's available
  gsap.registerPlugin(CSSPlugin);

  // Set default GSAP configuration
  gsap.defaults({
    ease: 'power2.out',
    duration: 0.8,
    overwrite: 'auto'
  });

  // Create animation utility functions
  const animations = {
    // Create a timeline with default options
    createTimeline(options = {}) {
      return gsap.timeline(options);
    },

    // Fade in from top animation
    fadeInFromTop(element, options = {}) {
      if (!element) return null;

      const defaults = {
        y: -30,
        opacity: 0,
        duration: 0.8,
        ease: 'power2.out'
      };

      const mergedOptions = { ...defaults, ...options };
      return gsap.from(element, mergedOptions);
    },

    // Fade in from bottom animation
    fadeInFromBottom(element, options = {}) {
      if (!element) return null;

      const defaults = {
        y: 20,
        opacity: 0,
        duration: 0.5,
        ease: 'power2.out'
      };

      const mergedOptions = { ...defaults, ...options };
      return gsap.from(element, mergedOptions);
    },

    // Fade in animation
    fadeIn(element, options = {}) {
      if (!element) return null;

      const defaults = {
        opacity: 0,
        duration: 0.5,
        ease: 'power2.out'
      };

      const mergedOptions = { ...defaults, ...options };
      return gsap.from(element, mergedOptions);
    },

    // Staggered fade in for multiple elements
    staggerFadeIn(elements, options = {}) {
      if (!elements || elements.length === 0) return null;

      const defaults = {
        opacity: 0,
        y: 20,
        duration: 0.6,
        stagger: 0.1,
        ease: 'power2.out'
      };

      const mergedOptions = { ...defaults, ...options };
      return gsap.from(elements, mergedOptions);
    },

    // Scale in animation
    scaleIn(element, options = {}) {
      if (!element) return null;

      const defaults = {
        scale: 0.8,
        opacity: 0,
        duration: 0.8,
        ease: 'power2.out'
      };

      const mergedOptions = { ...defaults, ...options };
      return gsap.from(element, mergedOptions);
    },

    // Slide in from left
    slideInFromLeft(element, options = {}) {
      if (!element) return null;

      const defaults = {
        x: -50,
        opacity: 0,
        duration: 0.8,
        ease: 'power2.out'
      };

      const mergedOptions = { ...defaults, ...options };
      return gsap.from(element, mergedOptions);
    },

    // Slide in from right
    slideInFromRight(element, options = {}) {
      if (!element) return null;

      const defaults = {
        x: 50,
        opacity: 0,
        duration: 0.8,
        ease: 'power2.out'
      };

      const mergedOptions = { ...defaults, ...options };
      return gsap.from(element, mergedOptions);
    }
  };

  // Hook into page transitions to clean up animations
  nuxtApp.hook('page:transition:finish', () => {
    // Kill any lingering animations to prevent memory leaks
    gsap.killTweensOf('*');

    // Clean up any ripple elements that might be stuck
    try {
      // Find all ripple elements in the DOM
      const ripples = document.querySelectorAll('.ripple');
      if (ripples.length > 0) {
        ripples.forEach(ripple => {
          if (ripple && ripple.parentNode) {
            ripple.parentNode.removeChild(ripple);
          }
        });
      }
    } catch (error) {
      console.warn('Error cleaning up ripples during page transition:', error);
    }
  });

  // Provide GSAP and animations to the app
  return {
    provide: {
      gsap,
      animations
    }
  };
});
