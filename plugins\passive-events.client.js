// Plugin to add passive event listeners to iframes and other elements
// This helps prevent the "Added non-passive event listener to a scroll-blocking event" warnings

export default defineNuxtPlugin((nuxtApp) => {
  // Only run in client-side
  if (typeof window === 'undefined') return;

  // Function to add passive event listeners to an element
  const addPassiveListeners = (element) => {
    if (!element) return;

    const events = ['touchstart', 'touchmove', 'touchend', 'wheel', 'mousewheel'];

    events.forEach(eventName => {
      element.addEventListener(eventName, (e) => {}, { passive: true });
    });
  };

  // Function to process all iframes on the page
  const processIframes = () => {
    // Get all iframes
    const iframes = document.querySelectorAll('iframe');

    iframes.forEach(iframe => {
      // Add passive listeners to the iframe
      addPassiveListeners(iframe);

      // Try to access iframe content if same origin
      try {
        if (iframe.contentDocument) {
          addPassiveListeners(iframe.contentDocument);

          // Also add to the body of the iframe if available
          if (iframe.contentDocument.body) {
            addPassiveListeners(iframe.contentDocument.body);
          }
        }
      } catch (e) {
        // Cross-origin restrictions may prevent access to iframe content
        // This is expected for YouTube and other third-party embeds
      }

      // Add load event listener to process iframe after it loads
      iframe.addEventListener('load', () => {
        try {
          addPassiveListeners(iframe);

          if (iframe.contentDocument) {
            addPassiveListeners(iframe.contentDocument);

            if (iframe.contentDocument.body) {
              addPassiveListeners(iframe.contentDocument.body);
            }
          }
        } catch (e) {
          // Cross-origin restrictions
        }
      });
    });
  };

  // Add passive event listeners to document and window
  const addGlobalPassiveListeners = () => {
    // Add to document
    addPassiveListeners(document);
    addPassiveListeners(document.body);

    // Add to all elements that might have scroll or touch events
    const scrollElements = document.querySelectorAll('div, section, article, main, aside, nav');
    scrollElements.forEach(element => {
      addPassiveListeners(element);
    });

    // Add to window for global events
    const windowEvents = ['touchstart', 'touchmove', 'touchend', 'wheel', 'mousewheel'];
    windowEvents.forEach(eventName => {
      window.addEventListener(eventName, (e) => {}, { passive: true });
    });
  };

  // Process iframes when the DOM is ready
  nuxtApp.hook('app:mounted', () => {
    // Add passive listeners to global elements
    addGlobalPassiveListeners();

    // Process iframes
    processIframes();

    // Set up a MutationObserver to watch for new elements
    const observer = new MutationObserver((mutations) => {
      let shouldProcessIframes = false;
      let shouldProcessElements = false;

      mutations.forEach(mutation => {
        if (mutation.addedNodes.length) {
          for (let i = 0; i < mutation.addedNodes.length; i++) {
            const node = mutation.addedNodes[i];

            // Check if the added node is an iframe or contains iframes
            if (node.nodeName === 'IFRAME' ||
                (node.nodeType === 1 && node.querySelector('iframe'))) {
              shouldProcessIframes = true;
            }

            // Check if the added node is a scrollable element
            if (node.nodeType === 1 &&
                ['DIV', 'SECTION', 'ARTICLE', 'MAIN', 'ASIDE', 'NAV'].includes(node.nodeName)) {
              shouldProcessElements = true;
            }
          }
        }
      });

      if (shouldProcessIframes) {
        processIframes();
      }

      if (shouldProcessElements) {
        addGlobalPassiveListeners();
      }
    });

    // Start observing the document with the configured parameters
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    // Clean up observer on page unload
    window.addEventListener('beforeunload', () => {
      observer.disconnect();
    });
  });
});
