// This plugin ensures locomotive-scroll is only loaded on the client side
// and provides a safe wrapper around its functionality

import LocomotiveScroll from 'locomotive-scroll'
import 'locomotive-scroll/dist/locomotive-scroll.css'

export default defineNuxtPlugin((nuxtApp) => {
  // Only run on client-side
  if (process.server) return

  // Create a safe wrapper around locomotive-scroll
  const locomotiveScroll = {
    // Store the instance
    _instance: null,

    // Initialize locomotive-scroll
    init(options = {}) {
      try {
        // Make sure we're on client-side with all required objects
        if (typeof window === 'undefined' || !window.document || !window.document.documentElement) {
          console.warn('Cannot initialize LocomotiveScroll: window or document not available')
          return null
        }

        // Make sure the element exists
        const el = options.el
        if (!el) {
          console.warn('Cannot initialize LocomotiveScroll: no element provided')
          return null
        }

        // Create a new instance with default settings
        this._instance = new LocomotiveScroll({
          ...options,
          // Ensure smooth scrolling is enabled
          smooth: options.smooth !== undefined ? options.smooth : true
        })

        return this._instance
      } catch (error) {
        console.error('Failed to initialize LocomotiveScroll:', error)
        return null
      }
    },

    // Get the instance
    getInstance() {
      return this._instance
    },

    // Scroll to a target
    scrollTo(target, options = {}) {
      try {
        if (!this._instance) {
          console.warn('Cannot scroll: LocomotiveScroll not initialized')
          return
        }

        this._instance.scrollTo(target, options)
      } catch (error) {
        console.error('Failed to scroll with LocomotiveScroll:', error)
      }
    },

    // Update the instance
    update() {
      try {
        if (!this._instance) {
          console.warn('Cannot update: LocomotiveScroll not initialized')
          return
        }

        this._instance.update()
      } catch (error) {
        console.error('Failed to update LocomotiveScroll:', error)
      }
    },

    // Destroy the instance
    destroy() {
      try {
        if (!this._instance) {
          return
        }

        this._instance.destroy()
        this._instance = null
      } catch (error) {
        console.error('Failed to destroy LocomotiveScroll:', error)
        this._instance = null
      }
    },

    // Add an event listener
    on(event, callback) {
      try {
        if (!this._instance) {
          console.warn('Cannot add event listener: LocomotiveScroll not initialized')
          return
        }

        this._instance.on(event, callback)
      } catch (error) {
        console.error(`Failed to add ${event} event listener to LocomotiveScroll:`, error)
      }
    },

    // Remove an event listener
    off(event, callback) {
      try {
        if (!this._instance) {
          return
        }

        this._instance.off(event, callback)
      } catch (error) {
        console.error(`Failed to remove ${event} event listener from LocomotiveScroll:`, error)
      }
    }
  }

  // Provide the locomotive-scroll wrapper to the app
  nuxtApp.provide('locomotiveScroll', locomotiveScroll)
})
