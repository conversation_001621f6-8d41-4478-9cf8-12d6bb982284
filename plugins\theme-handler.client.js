// Theme handler plugin following Nuxt best practices
import { useThemeStore } from '~/stores/theme';

export default defineNuxtPlugin((nuxtApp) => {
  // Get the theme store
  const themeStore = useThemeStore();
  
  // Initialize theme on app mounted
  nuxtApp.hook('app:mounted', () => {
    try {
      // Initialize the theme store with no route dependency
      if (themeStore && typeof themeStore.initializeTheme === 'function') {
        themeStore.initializeTheme();
      }
      
      // Apply theme immediately
      applyThemeToDOM();
    } catch (error) {
      console.error('Error initializing theme:', error);
    }
  });
  
  // Helper function to check if a path is a project page
  function isProjectPagePath(path) {
    if (!path) return false;
    
    return path.startsWith('/projects/') && 
           path !== '/projects/' && 
           path !== '/projects';
  }
  
  // Helper function to apply theme to DOM
  function applyThemeToDOM() {
    try {
      const themeClass = themeStore.themeClass;
      
      if (document && document.documentElement) {
        // Remove existing theme classes
        document.documentElement.classList.remove('theme-web');
        
        // Add new theme class if it exists
        if (themeClass && typeof themeClass === 'string' && themeClass.trim() !== '') {
          document.documentElement.classList.add(themeClass);
        }
        
        // Save theme preference
        themeStore.saveThemePreference();
      }
    } catch (error) {
      console.warn('Error applying theme to DOM:', error);
    }
  }
  
  // Provide a helper function to ensure theme is correct
  return {
    provide: {
      ensureTheme: (routePath) => {
        try {
          // Use the provided route path or get it from window.location
          const path = routePath || (window.location ? window.location.pathname : '');
          const isProjectPage = isProjectPagePath(path);
          
          // Update project page flag
          themeStore.isProjectPage = isProjectPage;
          
          // If we're not on a project page, ensure theme is applied
          if (!isProjectPage) {
            // Load theme preference from localStorage
            try {
              const savedTheme = localStorage.getItem('themePreference');
              if (savedTheme !== null) {
                themeStore.isGameDev = savedTheme === 'game';
              }
            } catch (error) {
              console.warn('Error loading theme preference:', error);
            }
            
            // Apply theme
            themeStore.applyTheme();
          }
          
          // Apply theme to DOM
          applyThemeToDOM();
        } catch (error) {
          console.error('Error ensuring theme:', error);
        }
      },
      
      // Helper to reset theme when leaving project pages
      resetTheme: () => {
        try {
          if (themeStore.isProjectPage) {
            themeStore.resetToDefaultTheme();
            applyThemeToDOM();
          }
        } catch (error) {
          console.error('Error resetting theme:', error);
        }
      }
    }
  };
});
