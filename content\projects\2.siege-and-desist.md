---
title: Siege and Desist
description: A mobile hero-based 2D tower defense game developed in Unity.
  Defend your castle from waves of enemies using a variety of heroes, each with
  unique abilities and playstyles. Earn rewards, unlock and upgrade heroes,
  turrets, and traps to build your ultimate defense.
coverImage: /images/projects/siegedesist.png
colors:
  primary: "#fec539"
  secondary: "#116396"
  accent: "#9b80e9"
  background: "#2a1e32"
techStack:
  - name: C#
    icon: devicon:csharp
  - name: Unity
    icon: bi:unity
  - name: PlayFab
    icon: logos:microsoft-icon
  - name: Google Play
    icon: logos:google-play-icon
  - name: GitHub
    icon: mdi:github
  - name: Trello
    icon: devicon:trello
tags:
  - type: Collaborative
    icon: mdi:account-group
roles:
  - Lead Programmer
  - Game Designer
info:
  table:
    - label: Duration
      value: 6 months
    - label: Platform
      value: Android
  buttons:
    - text: Sign Up for Closed Testing
      url: https://docs.google.com/forms/d/e/1FAIpQLSeAZFgaCMiv0GEEsCeJFqUL0AOUwyh-kuczHiGV2nrZYJt4Dw/viewform
carouselItems:
  - type: image
    src: /images/projects/siegedesist/1.png
    alt: Authentication Page
  - type: image
    src: /images/projects/siegedesist/2.png
    alt: Schedule Page
  - type: image
    src: /images/projects/siegedesist/3.png
    alt: Store Page
  - type: image
    src: /images/projects/siegedesist/4.png
    alt: Store Page - Dark Mode
seo:
  title: Siege and Desist | Fadi Nahhas
  description: A mobile hero-based 2D tower defense game developed in Unity.
navigation:
  title: Siege and Desist | Fadi Nahhas
---

# Siege and Desist

Defend, fight, and strategize in this thrilling tower defense and action hybrid! In Siege and Desist, you must protect your stronghold from relentless waves of enemies by building lethal traps and powerful turrets to halt them in their tracks.\:brThen, jump into the chaos yourself—charge into battle with your hero of choice and unleash devastating attacks on anything that dares to advance.

## Game Concept

Siege and Desist is a mobile game blending tower defense and hero-based combat. Players build traps and turrets to defend the portal to their homeland from enemy waves, then jump into battle with unique, upgradable heroes wielding powerful weapons. It’s a thrilling mix of strategy and action.

## Media

::gallery
---
alt: Defeat Waves of Enemies,Unlock New Items,Play and Earn Rewards,Upgrade Items
items: /images/projects/siege-and-desist/1.png,/images/projects/siege-and-desist/2.png,/images/projects/siege-and-desist/3.png,/images/projects/siege-and-desist/4.png
---
::

## Development Process

As the lead programmer, I was responsible for:

1. Developing all gameplay systems and mechanics
2. Building backend using PlayFab to manage player data, inventory, and economy
3. Integrating CAS.AI for in-game rewarded ads
4. Developing custom Unity editor tools, reducing development, iteration, and testing time.

## Technical Challenges

The main technical challenges for Siege and Desist revolved around optimizing the game for mobile performance across a wide range of devices.

- **Unity Addressables** - Enabled cloud-based asset loading (heroes, textures, levels), and implemented a data-based approach for traps, turrets, and enemies, significantly reducing prefab counts and simplifying asset management
- **Memory Optimization** - Used object pooling for enemies, projectiles, and effects; applied texture compression for minimal memory use.
- **Sprite Packing** - Combined sprites into atlases to reduce memory usage and draw times, enhancing performance for a 2D game.
