<template>
  <div>
    <!-- Particles background at the root level -->
    <ParticlesBackground />

    <div class="min-h-screen flex flex-col transition-colors duration-500 relative z-0">
      <Navigation />
      <main class="flex-grow">
        <NuxtPage />
      </main>
    </div>
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router';
import { watch, onMounted } from 'vue';
import { useNuxtApp } from '#app';

const nuxtApp = useNuxtApp();
const route = useRoute();

// Handle body class only in client-side
const updateBodyClass = (path) => {
  if (import.meta.client && document?.body) {
    if (path === '/') {
      // Home page - prevent scrolling
      document.body.classList.add('no-scroll-home');
    } else {
      // Other pages - allow scrolling
      document.body.classList.remove('no-scroll-home');
    }
  }
};

// Helper function to force scroll to top for project pages
const forceScrollToTopForProjects = (path) => {
  if (!import.meta.client) return;

  // Only for project detail pages
  if (path.startsWith('/projects/') && path !== '/projects/') {
    try {
      // Reset scroll position
      window.scrollTo(0, 0);

      // Safely access DOM elements
      if (document.documentElement) {
        document.documentElement.scrollTop = 0;
      }

      if (document.body) {
        document.body.scrollTop = 0;
      }
    } catch (error) {
      console.warn('Error scrolling to top:', error);
    }
  }
};

// Watch for route changes
watch(() => route.path, (newPath, oldPath) => {
  // Update body class
  updateBodyClass(newPath);

  // Force scroll to top for project pages
  forceScrollToTopForProjects(newPath);

  // Check if we're navigating away from a project page
  const isLeavingProjectPage = oldPath &&
    oldPath.startsWith('/projects/') &&
    oldPath !== '/projects/' &&
    oldPath !== '/projects' &&
    (!newPath.startsWith('/projects/') || newPath === '/projects/' || newPath === '/projects');

  if (isLeavingProjectPage && nuxtApp.$resetTheme) {
    // Reset theme when leaving project pages using the plugin helper
    nuxtApp.$resetTheme();
  }

  // Ensure theme is correct using our plugin helper
  if (nuxtApp.$ensureTheme) {
    nuxtApp.$ensureTheme(newPath);
  }

  // Clean up any ripple elements that might be stuck during navigation
  cleanupRippleElements();
}, { immediate: false });

// Helper function to clean up ripple elements
const cleanupRippleElements = () => {
  if (!import.meta.client) return;

  try {
    // Find all ripple elements in the DOM
    const ripples = document.querySelectorAll('.ripple');
    if (ripples.length > 0) {
      ripples.forEach(ripple => {
        if (ripple && ripple.parentNode) {
          ripple.parentNode.removeChild(ripple);
        }
      });
    }
  } catch (error) {
    console.warn('Error cleaning up ripples during navigation:', error);
  }
};

// Initialize on mount
onMounted(() => {
  // Update body class based on route
  updateBodyClass(route.path);

  // Force scroll to top for project pages
  forceScrollToTopForProjects(route.path);

  // Ensure theme is correct using our plugin helper
  if (nuxtApp.$ensureTheme) {
    nuxtApp.$ensureTheme(route.path);
  }

  // Clean up any ripple elements that might be stuck from previous sessions
  cleanupRippleElements();
});
</script>

<style>
html {
  overflow-x: hidden;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  background-color: var(--background-color);
  transition: background-color 0.5s ease;
}

body {
  overflow-x: hidden;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  /* No background color here - it inherits from html */
}

/* No scroll class for home page only */
body.no-scroll-home {
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
  height: 100% !important;
}

/* Default theme (Game Dev - Green) */
:root {
  /* Main colors */
  --primary-color: #00FF41;    /* Matrix green */
  --secondary-color: #008F11;  /* Darker green */
  --accent-color: #003B00;     /* Very dark green */
  --background-color: #121212; /* Dark background */

  /* RGB values for rgba usage */
  --primary-rgb: 0, 255, 65;
  --secondary-rgb: 0, 143, 17;
  --accent-rgb: 0, 59, 0;

  /* Transition for smooth theme changes */
  --theme-transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Web Dev Theme (Blue) */
html.theme-web {
  /* Main colors */
  --primary-color: #00A3FF;    /* Bright blue */
  --secondary-color: #0077B6;  /* Medium blue */
  --accent-color: #003D5B;     /* Dark blue */

  /* RGB values for rgba usage */
  --primary-rgb: 0, 163, 255;
  --secondary-rgb: 0, 119, 182;
  --accent-rgb: 0, 61, 91;
}

/* Add padding to main content to account for fixed navbar */
main {
  padding-top: 4rem; /* 64px for mobile */
}

/* Remove padding for project detail page */
main.project-detail-page {
  padding-top: 0;
}

/* Responsive adjustments */
@media (min-width: 768px) {
  main {
    padding-top: 5rem; /* 80px for desktop */
  }

  main.project-detail-page {
    padding-top: 0;
  }
}
</style>