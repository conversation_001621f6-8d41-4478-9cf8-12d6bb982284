<template>
  <h6 class="heading text-sm font-medium mt-4 mb-2" :style="headingStyle">
    <div class="heading-content">
      <slot />
    </div>
  </h6>
</template>

<script setup>
import { computed } from 'vue';
import { useThemeStore } from '~/stores/theme';

const props = defineProps({
  id: {
    type: String,
    default: ''
  }
});

// Get the theme store
const themeStore = useThemeStore();

// Convert hex to rgba
const hexToRgba = (hex, alpha = 1) => {
  if (!hex) return `rgba(0, 255, 65, ${alpha})`;
  
  // Remove # if present
  hex = hex.replace(/^#/, '');
  
  // Parse hex values
  let bigint = parseInt(hex, 16);
  let r = (bigint >> 16) & 255;
  let g = (bigint >> 8) & 255;
  let b = bigint & 255;
  
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

// Get primary and secondary colors from theme
const primaryColor = computed(() => themeStore.themeColors.primary || '#00FF41');

// Style for the heading text
const headingStyle = computed(() => {
  return {
    color: hexToRgba(primaryColor.value, 0.75)
  };
});
</script>

<style scoped>
.heading {
  position: relative;
}

.heading-content {
  position: relative;
  z-index: 1;
}
</style>
