---
title: Rehab Revolution
description: Rehab Revolution is a VR platform that aims to assist patients with
  at-home hand physiotherapy exercises. It features gamified and non-gamified
  exercise modes.
coverImage: /images/projects/rehabrevolution.png
colors:
  primary: "#C45900"
  secondary: "#dfe0df"
  accent: "#39beab"
  background: "#402e32"
techStack:
  - name: C#
    icon: devicon:csharp
  - name: Unity
    icon: bi:unity
  - name: Firebase
    icon: devicon:firebase
  - name: Vue.js
    icon: logos:vue
  - name: Vite
    icon: devicon:vitejs
  - name: TypeScript
    icon: devicon:typescript
  - name: GitHub
    icon: mdi:github
  - name: ClickUp
    icon: simple-icons:clickup
tags:
  - type: Individual
    icon: mdi:user
  - type: Client Project
    icon: mdi:handshake
  - type: Graduation Project
    icon: mdi:graduation-cap
roles:
  - Full-Stack Developer
  - Game Designer
info:
  table:
    - label: Duration
      value: 6 months
    - label: Platform
      value: Meta Quest, Web
  buttons:
    - text: Download for Quest
      url: https://drive.google.com/file/d/16qKo0BfL8itMaxvWg_-VibWKu1s0tzRn/view
---

# Rehab Revolution

Rehab Revolution is a VR platform that aims to assist patients with at-home hand physiotherapy exercises. It features gamified and non-gamified exercise modes. The game collects data on the patient's performance during each session and uploads it to a database. The data is then used by the patient's physiotherapist to track their progress.

## Features

- **VR Rehab Platform**: Core VR system for at-home hand therapy, using Meta Quest hand tracking.
- **Versatile Exercises**: Offers gamified and standard exercises adaptable for diverse patient groups.
- **Custom Exercise Editor**: Editor tools to easily create and modify patient exercises.
- **AI Voice Control**: Enables hands-free navigation and interaction using wit.ai.
- **Patient Data Tracking**: Securely tracks and stores patient peak performance data on Firebase for therapist access.
- **Web Dashboard**: Therapists can view patient data and progress on a web dashboard.
- **Clinician-Informed Design**: Developed in collaboration with physiotherapsists to ensure clinical relevance.
- **User-Tested Experience**: Platform usability and engagement refined through direct user feedback.

### Walkthrough

::youtube{#Y71sXxre19M aspect-ratio="16:9" title="Rehab Revolution Demo"}
::

## Project Background

This project involved the development of a prototype for the rehabilitation clinic at [Wilhelmina Ziekenhuis Assen (WZA)](https://www.wza.nl/).\:brThe initiative was driven by a national mandate in the Netherlands requiring hospitals to transition 25% of their services to digital platforms.\:brConsequently, the WZA clinic sought an innovative solution to offer its patients engaging and effective at-home rehabilitation programs.

## Technical Challenges

1. **Designing Safe, Enganging Gameplay**: Creating a fun and motivating game around hand exercises while ensuring movements are cautious and therapeutically sound to prevent injury and avoid a repetitive or tedious experience.
2. **Optimizing Local Tracking and Cloud Storage**: Efficiently processing real-time hand movement data locally for responsiveness and selectively identifying/storing only essential performance information on Firebase for optimized cloud usage.
