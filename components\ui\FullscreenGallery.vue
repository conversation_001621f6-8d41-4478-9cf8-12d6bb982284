<template>
  <Teleport to="body">
    <Transition name="fade">
      <div v-if="isOpen" class="fullscreen-gallery" @click.self="close">
        <!-- Close button -->
        <button 
          class="close-button" 
          @click="close"
          :style="{ color: primaryColor }"
        >
          <IconifyIcon icon="mdi:close" class="w-6 h-6" />
        </button>

        <!-- Navigation buttons -->
        <button 
          v-if="items.length > 1" 
          class="nav-button prev-button" 
          @click.stop="prevSlide"
          :style="{ color: primaryColor }"
        >
          <IconifyIcon icon="mdi:chevron-left" class="w-8 h-8" />
        </button>
        
        <button 
          v-if="items.length > 1" 
          class="nav-button next-button" 
          @click.stop="nextSlide"
          :style="{ color: primaryColor }"
        >
          <IconifyIcon icon="mdi:chevron-right" class="w-8 h-8" />
        </button>

        <!-- Main content -->
        <div class="gallery-content">
          <Transition name="slide" mode="out-in">
            <div :key="currentIndex" class="slide-container">
              <!-- Image slide -->
              <img 
                v-if="currentItem.type === 'image'" 
                :src="currentItem.src" 
                :alt="currentItem.alt || ''" 
                class="slide-content"
              />
              
              <!-- Video slide -->
              <video 
                v-else-if="currentItem.type === 'video'" 
                :src="currentItem.src" 
                controls 
                autoplay 
                class="slide-content"
              ></video>
            </div>
          </Transition>
        </div>

        <!-- Caption -->
        <div v-if="currentItem.alt" class="caption" :style="{ color: primaryColor }">
          {{ currentItem.alt }}
        </div>

        <!-- Thumbnails -->
        <div v-if="items.length > 1" class="thumbnails">
          <div 
            v-for="(item, index) in items" 
            :key="index" 
            class="thumbnail" 
            :class="{ active: index === currentIndex }"
            @click="goToSlide(index)"
            :style="{ borderColor: index === currentIndex ? primaryColor : 'transparent' }"
          >
            <img v-if="item.type === 'image'" :src="item.src" :alt="item.alt || ''" />
            <div v-else-if="item.type === 'video'" class="video-thumbnail">
              <IconifyIcon icon="mdi:play" class="w-6 h-6" />
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue';
import { useThemeStore } from '~/stores/theme';

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  items: {
    type: Array,
    default: () => []
  },
  initialIndex: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['close', 'change']);

// State
const currentIndex = ref(props.initialIndex);
const themeStore = useThemeStore();

// Computed properties
const primaryColor = computed(() => {
  return themeStore.themeColors.primary;
});

const currentItem = computed(() => {
  return props.items[currentIndex.value] || { type: 'image', src: '', alt: '' };
});

// Methods
const close = () => {
  emit('close');
};

const nextSlide = () => {
  currentIndex.value = (currentIndex.value + 1) % props.items.length;
  emit('change', currentIndex.value);
};

const prevSlide = () => {
  currentIndex.value = (currentIndex.value - 1 + props.items.length) % props.items.length;
  emit('change', currentIndex.value);
};

const goToSlide = (index) => {
  currentIndex.value = index;
  emit('change', currentIndex.value);
};

// Keyboard navigation
const handleKeyDown = (e) => {
  if (!props.isOpen) return;
  
  switch (e.key) {
    case 'Escape':
      close();
      break;
    case 'ArrowRight':
      nextSlide();
      break;
    case 'ArrowLeft':
      prevSlide();
      break;
  }
};

// Watch for prop changes
watch(() => props.initialIndex, (newIndex) => {
  currentIndex.value = newIndex;
});

watch(() => props.isOpen, (newValue) => {
  if (newValue) {
    // When gallery opens, prevent body scrolling
    document.body.style.overflow = 'hidden';
  } else {
    // When gallery closes, restore body scrolling
    document.body.style.overflow = '';
  }
});

// Lifecycle hooks
onMounted(() => {
  window.addEventListener('keydown', handleKeyDown);
});

onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleKeyDown);
  // Ensure body scrolling is restored
  document.body.style.overflow = '';
});
</script>

<style scoped>
.fullscreen-gallery {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  backdrop-filter: blur(5px);
}

.close-button {
  position: absolute;
  top: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
}

.close-button:hover {
  transform: scale(1.1);
  background-color: rgba(0, 0, 0, 0.7);
}

.nav-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(0, 0, 0, 0.5);
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
}

.prev-button {
  left: 20px;
}

.next-button {
  right: 20px;
}

.nav-button:hover {
  transform: translateY(-50%) scale(1.1);
  background-color: rgba(0, 0, 0, 0.7);
}

.gallery-content {
  width: 100%;
  height: 80%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.slide-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.slide-content {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
  border-radius: 4px;
}

.caption {
  margin-top: 10px;
  padding: 10px;
  text-align: center;
  max-width: 80%;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
}

.thumbnails {
  position: absolute;
  bottom: 20px;
  display: flex;
  justify-content: center;
  gap: 10px;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 8px;
  overflow-x: auto;
  max-width: 80%;
}

.thumbnail {
  width: 60px;
  height: 40px;
  border: 2px solid transparent;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.thumbnail.active {
  transform: scale(1.1);
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-thumbnail {
  width: 100%;
  height: 100%;
  background-color: #333;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
}

.slide-enter-from {
  opacity: 0;
  transform: translateX(50px);
}

.slide-leave-to {
  opacity: 0;
  transform: translateX(-50px);
}
</style>
