# Nuxt/Vue build output
.nuxt
dist
.output
.nitro
.cache
.data

# Node modules
node_modules

# Logs
logs
*.log*
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.DS_Store

# Local env files
.env
.env.*
!.env.example

# Yarn
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions
.pnp.*

# Coverage directory used by tools like istanbul
coverage
*.lcov

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Cypress
cypress/videos
cypress/screenshots

# Temporary files
*.tmp
*.temp
.temp
.tmp

# Generated files
public/sw.js
public/workbox-*.js

# Vercel
.vercel

# Netlify
.netlify

# Firebase
.firebase
firebase-debug.log