<template>
  <h3 class="heading text-xl font-semibold mt-8 mb-3 pl-2" :style="headingStyle">
    <div class="heading-content">
      <slot />
    </div>
    <div class="heading-decoration" :style="decorationStyle"></div>
  </h3>
</template>

<script setup>
import { computed } from 'vue';
import { useThemeStore } from '~/stores/theme';

const props = defineProps({
  id: {
    type: String,
    default: ''
  }
});

// Get the theme store
const themeStore = useThemeStore();

// Convert hex to rgba
const hexToRgba = (hex, alpha = 1) => {
  if (!hex) return `rgba(0, 255, 65, ${alpha})`;
  
  // Remove # if present
  hex = hex.replace(/^#/, '');
  
  // Parse hex values
  let bigint = parseInt(hex, 16);
  let r = (bigint >> 16) & 255;
  let g = (bigint >> 8) & 255;
  let b = bigint & 255;
  
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

// Get primary and secondary colors from theme
const primaryColor = computed(() => themeStore.themeColors.primary || '#00FF41');
const secondaryColor = computed(() => themeStore.themeColors.secondary || '#347474');

// Style for the heading decoration
const decorationStyle = computed(() => {
  const color = secondaryColor.value;
  return {
    backgroundColor: hexToRgba(color, 0.15),
    borderLeft: `3px solid ${color}`,
    boxShadow: `0 0 10px ${hexToRgba(color, 0.3)}`
  };
});

// Style for the heading text
const headingStyle = computed(() => {
  return {
    color: hexToRgba(primaryColor.value, 0.9)
  };
});
</script>

<style scoped>
.heading {
  position: relative;
}

.heading-decoration {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 100%;
  border-radius: 0.25rem;
  z-index: -1;
}

.heading-content {
  position: relative;
  z-index: 1;
}
</style>
