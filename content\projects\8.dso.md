---
title: Digital Society Online
description: Digital Society Online is a VR game that is meant to connect you
  with the right people to develop your ideas.
coverImage: /images/projects/dso.png
colors:
  primary: "#0071d8"
  secondary: "#ffffff"
  accent: "#e5b254"
  background: "#303417"
techStack:
  - name: C#
    icon: devicon:csharp
  - name: Unity
    icon: bi:unity
  - name: GitHub
    icon: mdi:github
  - name: Trello
    icon: devicon:trello
tags:
  - type: Group
    icon: mdi:account-group
  - type: University Project
    icon: mdi:graduation-cap
roles:
  - Lead Programmer
  - Game Designer
info:
  table:
    - label: Duration
      value: 3 days
    - label: Platform
      value: Meta Quest
  buttons:
    - text: Play Game
      url: https://gx.games/games/kk5imy/escapod/
---

# Digital Society Online

Digital Society Online is a VR game that is meant to connect you with the right people to develop your ideas. It was developed to help people collaborate on projects and ideas during the COVID-19 pandemic. The game has different environments that have distinct purposes like fun, professional, and creative.:brThis project was made as a prototype for clients from the [Digital Society Hub](https://www.digitalsocietyhub.nl/) and the [Assen TechHub](https://www.techhubassen.nl/).

## Features Walkthrough

::youtube{#DyO7Awvp51g aspect-ratio="16:9" title="Digital Society Online Demo"}
::

## Contribution

### Programming

- **Archery Minigame**: Engineered a physics-driven archery system, simulating realistic arrow ballistics and impact interactions within the game environment
- **3D Painting System**: providing users with dynamic brush control (color, size) and the ability to manipulate pre-defined 3D shapes for creative expression
- **Sticky Note System**: Developed a persistent sticky note system for an in-game VR office environment, featuring a dedicated VR keyboard for text entry and allowing users to create and place notes on designated surfaces like whiteboards
- **Meta Quest SDK Integration**: Integrated the Meta Quest SDK to establish core VR functionalities, including head-tracking, hand controller input mapping, and interactive object manipulation mechanics.

### Design & Art

- **Environment Design**: Contributed to the visual design and layout of game environments
- **Lighting**: Designed and implemented scene lighting, including baking for performance and atmosphere
- **Basic 3D Art**: Created basic 3D models and generated UV maps for environment assets
- **UI Design**: Designed and implemented the user interface specifically for a virtual reality experience, focusing on usability and immersion within a 3D environment.
