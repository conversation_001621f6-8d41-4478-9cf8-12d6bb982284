// Asset optimization composable for better performance and caching
import { ref, computed } from 'vue'

// Asset cache for better performance
const assetCache = new Map()
const preloadedAssets = new Set()

export const useAssetOptimization = () => {
  const loadingAssets = ref(new Set())
  const failedAssets = ref(new Set())

  // Optimized image loading with WebP support and responsive sizing
  const optimizeImageSrc = (src, options = {}) => {
    const {
      width = null,
      height = null,
      quality = 80,
      format = 'auto', // 'auto', 'webp', 'jpg', 'png'
      responsive = true
    } = options

    if (!src) return ''

    // Return cached result if available
    const cacheKey = `${src}-${width}-${height}-${quality}-${format}`
    if (assetCache.has(cacheKey)) {
      return assetCache.get(cacheKey)
    }

    let optimizedSrc = src

    // Add query parameters for optimization (if using a service like Cloudinary, ImageKit, etc.)
    const params = new URLSearchParams()
    
    if (width) params.append('w', width.toString())
    if (height) params.append('h', height.toString())
    if (quality !== 80) params.append('q', quality.toString())
    
    // Auto-detect WebP support
    if (format === 'auto') {
      if (typeof window !== 'undefined' && window.navigator) {
        const canvas = document.createElement('canvas')
        const supportsWebP = canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0
        if (supportsWebP) {
          params.append('f', 'webp')
        }
      }
    } else if (format !== 'original') {
      params.append('f', format)
    }

    // Append optimization parameters if any
    if (params.toString()) {
      const separator = src.includes('?') ? '&' : '?'
      optimizedSrc = `${src}${separator}${params.toString()}`
    }

    // Cache the result
    assetCache.set(cacheKey, optimizedSrc)
    
    return optimizedSrc
  }

  // Generate responsive image srcset
  const generateSrcSet = (src, breakpoints = [320, 640, 768, 1024, 1280, 1536]) => {
    if (!src) return ''

    const srcsetEntries = breakpoints.map(width => {
      const optimizedSrc = optimizeImageSrc(src, { width, quality: 80 })
      return `${optimizedSrc} ${width}w`
    })

    return srcsetEntries.join(', ')
  }

  // Preload critical assets
  const preloadAsset = (src, type = 'image', priority = 'high') => {
    if (!src || preloadedAssets.has(src)) return Promise.resolve()

    return new Promise((resolve, reject) => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.href = src
      
      // Set appropriate attributes based on asset type
      switch (type) {
        case 'image':
          link.as = 'image'
          break
        case 'font':
          link.as = 'font'
          link.crossOrigin = 'anonymous'
          break
        case 'script':
          link.as = 'script'
          break
        case 'style':
          link.as = 'style'
          break
        default:
          link.as = 'fetch'
          link.crossOrigin = 'anonymous'
      }

      // Set priority if supported
      if ('fetchPriority' in link) {
        link.fetchPriority = priority
      }

      link.onload = () => {
        preloadedAssets.add(src)
        resolve()
      }
      
      link.onerror = () => {
        failedAssets.value.add(src)
        reject(new Error(`Failed to preload ${type}: ${src}`))
      }

      document.head.appendChild(link)
    })
  }

  // Batch preload multiple assets
  const preloadAssets = async (assets) => {
    const promises = assets.map(asset => {
      if (typeof asset === 'string') {
        return preloadAsset(asset)
      } else {
        return preloadAsset(asset.src, asset.type, asset.priority)
      }
    })

    return Promise.allSettled(promises)
  }

  // Lazy load asset with intersection observer
  const lazyLoadAsset = (src, element, options = {}) => {
    const {
      threshold = 0.1,
      rootMargin = '50px',
      type = 'image'
    } = options

    if (!src || !element) return Promise.resolve()

    return new Promise((resolve, reject) => {
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              loadingAssets.value.add(src)
              
              // Load the asset
              if (type === 'image') {
                const img = new Image()
                img.onload = () => {
                  loadingAssets.value.delete(src)
                  observer.disconnect()
                  resolve(img)
                }
                img.onerror = () => {
                  loadingAssets.value.delete(src)
                  failedAssets.value.add(src)
                  observer.disconnect()
                  reject(new Error(`Failed to load image: ${src}`))
                }
                img.src = src
              } else {
                // For other asset types, use fetch
                fetch(src)
                  .then(response => {
                    if (!response.ok) throw new Error(`HTTP ${response.status}`)
                    return response
                  })
                  .then(() => {
                    loadingAssets.value.delete(src)
                    observer.disconnect()
                    resolve()
                  })
                  .catch(error => {
                    loadingAssets.value.delete(src)
                    failedAssets.value.add(src)
                    observer.disconnect()
                    reject(error)
                  })
              }
            }
          })
        },
        { threshold, rootMargin }
      )

      observer.observe(element)
    })
  }

  // Get optimized font loading CSS
  const getFontLoadingCSS = (fontFamily, fontFiles) => {
    const fontFaceRules = fontFiles.map(file => {
      const { src, weight = 'normal', style = 'normal', display = 'swap' } = file
      
      return `
        @font-face {
          font-family: '${fontFamily}';
          src: url('${src}') format('woff2');
          font-weight: ${weight};
          font-style: ${style};
          font-display: ${display};
        }
      `
    }).join('\n')

    return fontFaceRules
  }

  // Preload critical fonts
  const preloadFonts = (fonts) => {
    return Promise.all(
      fonts.map(font => preloadAsset(font.src, 'font', 'high'))
    )
  }

  // Get asset loading statistics
  const getLoadingStats = computed(() => ({
    loading: loadingAssets.value.size,
    failed: failedAssets.value.size,
    preloaded: preloadedAssets.size,
    cached: assetCache.size
  }))

  // Clear asset cache
  const clearCache = () => {
    assetCache.clear()
    preloadedAssets.clear()
    loadingAssets.value.clear()
    failedAssets.value.clear()
  }

  // Retry failed assets
  const retryFailedAssets = async () => {
    const failedList = Array.from(failedAssets.value)
    failedAssets.value.clear()
    
    const retryPromises = failedList.map(src => 
      preloadAsset(src).catch(() => {
        failedAssets.value.add(src)
      })
    )
    
    return Promise.allSettled(retryPromises)
  }

  // Optimize video loading
  const optimizeVideo = (src, options = {}) => {
    const {
      preload = 'metadata', // 'none', 'metadata', 'auto'
      muted = true,
      loop = false,
      autoplay = false,
      poster = null
    } = options

    return {
      src,
      preload,
      muted,
      loop,
      autoplay,
      poster,
      // Add loading optimization attributes
      loading: 'lazy',
      decoding: 'async'
    }
  }

  return {
    // Image optimization
    optimizeImageSrc,
    generateSrcSet,
    
    // Asset preloading
    preloadAsset,
    preloadAssets,
    lazyLoadAsset,
    
    // Font optimization
    getFontLoadingCSS,
    preloadFonts,
    
    // Video optimization
    optimizeVideo,
    
    // Statistics and management
    getLoadingStats,
    clearCache,
    retryFailedAssets,
    
    // State
    loadingAssets: computed(() => loadingAssets.value),
    failedAssets: computed(() => failedAssets.value)
  }
}

// Global asset optimization utilities
export const createOptimizedImageUrl = (src, width, height, quality = 80) => {
  const { optimizeImageSrc } = useAssetOptimization()
  return optimizeImageSrc(src, { width, height, quality })
}

export const createResponsiveImageSet = (src, breakpoints) => {
  const { generateSrcSet } = useAssetOptimization()
  return generateSrcSet(src, breakpoints)
}
