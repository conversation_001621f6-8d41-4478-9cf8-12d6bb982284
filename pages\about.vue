<template>
  <div id="fullpage" class="fullpage-container">
    <!-- SEO Component -->
    <SEO
      title="About Me"
      description="Learn about my background, skills, and experience as a software engineer specializing in game development and web development."
      url="/about"
    />

    <!-- Horizontal Slider Container -->
    <div ref="sliderContainer" class="slider-container">
      <!-- First Section: About Me -->
      <section id="about-section" class="section">
        <AboutSection ref="aboutSection" />
      </section>

      <!-- Second Section: Skills Grid -->
      <section id="skills-section" class="section">
        <SkillsSection />
      </section>
    </div>

    <!-- Combined Navigation Controls -->
    <div class="bottom-navigation">
      <!-- Left Arrow -->
      <button
        @click="goToSection(currentSection - 1)"
        class="nav-arrow left-arrow"
        :class="{ 'disabled': currentSection === 0 }"
        :style="{ color: arrowColor }"
      >
        <Icon icon="mdi:chevron-left" class="w-6 h-6 md:w-7 md:h-7" />
      </button>

      <!-- Navigation Dots -->
      <div class="navigation-dots">
        <button
          v-for="(_, index) in 2"
          :key="index"
          @click="goToSection(index)"
          class="nav-dot"
          :class="{ active: currentSection === index }"
          :style="{ backgroundColor: currentSection === index ? dotColor : 'rgba(255, 255, 255, 0.3)' }"
        ></button>
      </div>

      <!-- Right Arrow -->
      <button
        @click="goToSection(currentSection + 1)"
        class="nav-arrow right-arrow"
        :class="{ 'disabled': currentSection === 1 }"
        :style="{ color: arrowColor }"
      >
        <Icon icon="mdi:chevron-right" class="w-6 h-6 md:w-7 md:h-7" />
      </button>
    </div>
  </div>
</template>

<script setup>
// SSR is disabled globally in nuxt.config.ts

import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import { useNuxtApp } from '#app';
import { Icon } from '@iconify/vue';
import AboutSection from '~/components/about/AboutSection.vue';
import SkillsSection from '~/components/about/SkillsSection.vue';
import { useThemeStore } from '~/stores/theme';

// Get the theme store
const themeStore = useThemeStore();

// Refs for DOM elements
const sliderContainer = ref(null);
const aboutSection = ref(null);

// SEO is now handled by the SEO component

// Reactive state
const state = reactive({
  currentSection: 0,
  isAnimating: false,
  touchStartX: 0,
  touchEndX: 0,
  wheelTimeout: null,
  resizeTimeout: null,
  sectionWidth: 0,
});

// Computed properties for theme-based styling
const dotColor = computed(() => themeStore.themeColors.primary || '#00FF41');
const arrowColor = computed(() => themeStore.themeColors.primary || '#00FF41');

// Expose current section to template
const currentSection = computed(() => state.currentSection);

// Function to navigate to a specific section
const goToSection = (index) => {
  // Validate index and check if animation is in progress
  if (state.isAnimating || index < 0 || index > 1) return;

  // Get the GSAP instance from the Nuxt app
  const { $gsap: gsap } = useNuxtApp();

  // Make sure GSAP is available
  if (!gsap) {
    console.warn('GSAP not available, animations skipped');
    return;
  }

  // Set animating state
  state.isAnimating = true;

  // Update current section
  state.currentSection = index;

  // Calculate target position
  const targetX = -index * state.sectionWidth;

  // Create a master timeline for better synchronization
  const masterTl = gsap.timeline({
    onComplete: () => {
      state.isAnimating = false;
    }
  });

  // Get the sections
  const sections = document.querySelectorAll('.section');

  // Add the main slider animation to the timeline
  masterTl.to(sliderContainer.value, {
    x: targetX,
    duration: 0.5,
    ease: "power2.out"
  }, 0);

  // Add parallax effect to the sections
  sections.forEach((section, i) => {
    const direction = i < index ? -1 : 1;
    const distance = i === index ? 0 : 40 * direction;

    masterTl.to(section, {
      x: distance,
      duration: 0.5,
      ease: "power2.out"
    }, 0); // Start at the same time as the main animation
  });

  // Add a subtle scale and opacity effect for better visual transition
  masterTl.to(sections[index], {
    scale: 1,
    opacity: 1,
    duration: 0.8,
    ease: "power2.out"
  }, 0);

  masterTl.to(sections[index === 0 ? 1 : 0], {
    scale: 0.95,
    opacity: 0.9,
    duration: 0.8,
    ease: "power2.out"
  }, 0);

  // Ensure both sections are visible during transitions
  gsap.set(sections, { visibility: 'visible' });

  // Add a subtle flash effect to the navigation dot
  const activeDot = document.querySelector(`.nav-dot:nth-child(${index + 1})`);
  if (activeDot) {
    masterTl.to(activeDot, {
      boxShadow: `0 0 15px rgba(var(--primary-rgb), 0.8)`,
      duration: 0.3,
      ease: "power2.inOut"
    }, 0.3)
    .to(activeDot, {
      boxShadow: `0 0 15px rgba(var(--primary-rgb), 0.5)`,
      duration: 0.5,
      ease: "power2.out"
    }, 0.6);
  }
};

// Handle wheel events for horizontal scrolling with improved debounce
const handleWheel = (e) => {
  // Prevent default to avoid any native scrolling
  e.preventDefault();

  // If animation is in progress, ignore wheel events
  if (state.isAnimating) return;

  // Clear any existing timeout to implement proper debounce
  if (state.wheelTimeout) {
    clearTimeout(state.wheelTimeout);
  }

  // Accumulate scroll delta to detect intentional scrolls
  const delta = Math.abs(e.deltaX) > Math.abs(e.deltaY) ? e.deltaX : e.deltaY;

  // Set a timeout with a shorter delay for more responsive feel
  state.wheelTimeout = setTimeout(() => {
    // Determine scroll direction with a threshold to avoid accidental scrolls
    if (delta > 50) {
      // Scroll right/down
      goToSection(state.currentSection + 1);
    } else if (delta < -50) {
      // Scroll left/up
      goToSection(state.currentSection - 1);
    }
  }, 30);
};

// Touch events for mobile removed to prevent unwanted swiping behavior

// Handle keyboard navigation
const handleKeyDown = (e) => {
  if (state.isAnimating) return;

  if (e.key === 'ArrowRight' || e.key === 'PageDown') {
    goToSection(state.currentSection + 1);
    e.preventDefault();
  } else if (e.key === 'ArrowLeft' || e.key === 'PageUp') {
    goToSection(state.currentSection - 1);
    e.preventDefault();
  }
};

// Update section width on resize with debounce for better performance
const handleResize = () => {
  if (!sliderContainer.value) return;

  // Get the GSAP instance from the Nuxt app
  const { $gsap: gsap } = useNuxtApp();

  // Make sure GSAP is available
  if (!gsap) {
    console.warn('GSAP not available, resize handling skipped');
    return;
  }

  // Use requestAnimationFrame for smoother updates
  requestAnimationFrame(() => {
    // Get the new section width
    state.sectionWidth = window.innerWidth;

    // Update the slider position without animation
    const targetX = -state.currentSection * state.sectionWidth;
    gsap.set(sliderContainer.value, { x: targetX });

    // Reset any parallax effects with improved positioning
    const sections = document.querySelectorAll('.section');
    sections.forEach((section, i) => {
      const isActive = i === state.currentSection;

      // Apply appropriate transforms based on section status
      gsap.set(section, {
        x: isActive ? 0 : (i < state.currentSection ? -40 : 40),
        scale: isActive ? 1 : 0.95,
        opacity: isActive ? 1 : 0.9
      });
    });
  });
};

// Initialize the page with improved animations and refresh handling
const initPage = () => {
  // Set initial section width
  state.sectionWidth = window.innerWidth;

  // Get the GSAP instance from the Nuxt app
  const { $gsap: gsap } = useNuxtApp();

  // Make sure GSAP is available
  if (!gsap) {
    console.warn('GSAP not available, animations skipped');
    return;
  }

  // Ensure the layout is correct regardless of how the page was loaded
  // This is crucial for handling page refreshes
  const sections = document.querySelectorAll('.section');

  // Force correct layout before any animations
  if (sliderContainer.value) {
    // Reset the slider position to show the first section
    gsap.set(sliderContainer.value, {
      x: -state.currentSection * state.sectionWidth
    });

    // Reset section positions to prevent side-by-side rendering on refresh
    sections.forEach((section, i) => {
      const isActive = i === state.currentSection;

      // Apply transforms to ensure sections are not side by side
      gsap.set(section, {
        x: isActive ? 0 : (i < state.currentSection ? -40 : 40),
        scale: isActive ? 1 : 0.95,
        opacity: isActive ? 1 : 0.9
      });
    });
  }

  // Short delay to ensure DOM is fully ready
  setTimeout(() => {
    // Create a master timeline for initial animations
    const initTl = gsap.timeline({
      defaults: {
        ease: "power2.out",
        duration: 0.6
      }
    });

    // Add initial animation for the active section
    initTl.from(sections[state.currentSection], {
      y: 15,
      opacity: 0.7,
      duration: 0.7
    }).to(sections[state.currentSection], {
      opacity: 1,
      duration: 0.3
    }, "-=0.3");

    // Animate navigation elements with a slight delay
    initTl.from('.bottom-navigation', {
      opacity: 0,
      y: 10,
      duration: 0.5
    }, "-=0.3");

    // Add a subtle highlight to the active navigation dot
    const activeDotSelector = `.nav-dot:nth-child(${state.currentSection + 1})`;
    initTl.to(activeDotSelector, {
      boxShadow: `0 0 15px rgba(var(--primary-rgb), 0.8)`,
      duration: 0.4
    }, "-=0.2")
    .to(activeDotSelector, {
      boxShadow: `0 0 15px rgba(var(--primary-rgb), 0.5)`,
      duration: 0.5
    });
  }, 100);
};

onMounted(() => {
  // Add a class to the body to prevent scrolling only on the about page
  document.body.classList.add('about-page-active');

  // Create a debounced resize handler for better performance
  const debouncedResize = () => {
    if (state.resizeTimeout) {
      clearTimeout(state.resizeTimeout);
    }
    state.resizeTimeout = setTimeout(handleResize, 100);
  };

  // Add event listeners with proper options
  window.addEventListener('wheel', handleWheel, { passive: false });
  window.addEventListener('keydown', handleKeyDown);
  window.addEventListener('resize', debouncedResize);

  // Get the GSAP instance from the Nuxt app
  const { $gsap: gsap } = useNuxtApp();

  // Force an initial resize to ensure correct layout
  handleResize();

  // Set initial state for sections
  const sections = document.querySelectorAll('.section');
  if (sections.length === 2 && sliderContainer.value) {
    // Set initial position for slider
    gsap.set(sliderContainer.value, {
      x: 0,
      opacity: 1
    });

    // Set initial state for sections
    gsap.set(sections[0], {
      x: 0,
      scale: 1,
      opacity: 1
    });

    gsap.set(sections[1], {
      x: 40,
      scale: 0.95,
      opacity: 0.9
    });
  }

  // Use a short delay before initialization to ensure DOM is fully ready
  setTimeout(() => {
    // Initialize the page with animations
    initPage();

    // Force another resize after a short delay to ensure everything is positioned correctly
    setTimeout(handleResize, 100);
  }, 100);
});

onUnmounted(() => {
  // Clean up all timeouts
  if (state.wheelTimeout) clearTimeout(state.wheelTimeout);
  if (state.resizeTimeout) clearTimeout(state.resizeTimeout);

  // Clean up event listeners
  window.removeEventListener('wheel', handleWheel);
  window.removeEventListener('keydown', handleKeyDown);
  window.removeEventListener('resize', handleResize);

  // Remove the class from the body when leaving the about page
  document.body.classList.remove('about-page-active');

  // Get the GSAP instance from the Nuxt app
  const { $gsap: gsap } = useNuxtApp();

  // Kill any running GSAP animations to prevent memory leaks
  if (gsap) {
    gsap.killTweensOf(document.querySelectorAll('.section'));
    gsap.killTweensOf(sliderContainer.value);
    gsap.killTweensOf('.nav-dot');
    gsap.killTweensOf('.bottom-navigation');
  }
});
</script>

<style>
/* Prevent any scrolling on the body when on the about page - using a specific class */
:global(body.about-page-active) {
  overflow: hidden !important;
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
}

.fullpage-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.slider-container {
  position: absolute;
  display: flex;
  width: 200%; /* Two sections side by side */
  height: 100%;
  will-change: transform;
  top: 0;
  left: 0;
  transform: translateX(0); /* Default position showing first section */
  transition: transform 0.3s ease; /* Smooth transition for manual adjustments */
}

.section {
  position: relative;
  flex: 0 0 50%; /* Each section takes half of the slider width */
  width: 50%;
  height: 100%;
  overflow: hidden;
  will-change: transform;
  display: flex;
  flex-direction: column;
  transform: translateX(0) scale(1); /* Default transform to prevent layout issues */
  opacity: 1; /* Visible by default for SSR */
}

/* Default styling for the second section to prevent side-by-side rendering on refresh */
.section:nth-child(2) {
  transform: translateX(40px) scale(0.95); /* Default position for inactive section */
  opacity: 0.9; /* Slightly transparent by default */
}

/* Bottom navigation container */
.bottom-navigation {
  position: fixed;
  bottom: 30px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  z-index: 10;
  padding: 0 20px;
}

/* Navigation dots */
.navigation-dots {
  display: flex;
  gap: 12px;
  z-index: 10;
}

.nav-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.nav-dot.active {
  transform: scale(1.2);
  box-shadow: 0 0 15px rgba(var(--primary-rgb), 0.5);
}

/* Navigation arrows */
.nav-arrow {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0.7;
}

.nav-arrow:hover {
  background-color: rgba(0, 0, 0, 0.5);
  transform: scale(1.1);
  opacity: 1;
}

.nav-arrow.disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

/* Ensure the snap-section class still works with our new approach */
.snap-section {
  height: 100%;
  width: 100%;
  max-height: 100%;
  overflow: hidden;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .bottom-navigation {
    bottom: 20px;
    gap: 15px;
  }

  .nav-arrow {
    width: 36px;
    height: 36px;
  }

  .nav-dot {
    width: 10px;
    height: 10px;
  }
}
</style>