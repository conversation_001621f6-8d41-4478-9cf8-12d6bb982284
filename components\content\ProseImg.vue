<template>
  <div class="my-6 relative">
    <img 
      :src="src" 
      :alt="alt" 
      class="rounded-lg shadow-lg w-full max-w-full"
      :class="{ 'cursor-pointer': zoomable }"
      @click="zoomable && toggleZoom()"
      ref="imageRef"
    />
    
    <!-- Image caption if provided -->
    <div 
      v-if="alt && showCaption" 
      class="text-center text-sm text-gray-400 mt-2 italic"
    >
      {{ alt }}
    </div>
    
    <!-- Zoomed overlay -->
    <div 
      v-if="isZoomed" 
      class="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4"
      @click="toggleZoom()"
    >
      <div class="relative max-w-screen-xl max-h-screen">
        <img 
          :src="src" 
          :alt="alt" 
          class="max-w-full max-h-[90vh] object-contain"
        />
        <button 
          class="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-70 transition-all duration-300"
          @click.stop="toggleZoom()"
        >
          <IconifyIcon icon="mdi:close" class="w-6 h-6" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const props = defineProps({
  src: {
    type: String,
    required: true
  },
  alt: {
    type: String,
    default: ''
  },
  showCaption: {
    type: Boolean,
    default: true
  },
  zoomable: {
    type: Boolean,
    default: true
  }
});

const isZoomed = ref(false);
const imageRef = ref(null);

// Toggle zoom state
const toggleZoom = () => {
  isZoomed.value = !isZoomed.value;
  
  // Prevent body scrolling when zoomed
  if (isZoomed.value) {
    document.body.style.overflow = 'hidden';
  } else {
    document.body.style.overflow = '';
  }
};

// Add escape key listener to close zoom
onMounted(() => {
  const handleEscape = (e) => {
    if (e.key === 'Escape' && isZoomed.value) {
      toggleZoom();
    }
  };
  
  window.addEventListener('keydown', handleEscape);
  
  // Clean up
  onUnmounted(() => {
    window.removeEventListener('keydown', handleEscape);
    // Ensure body scrolling is restored
    document.body.style.overflow = '';
  });
});
</script>
