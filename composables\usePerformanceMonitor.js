// Performance monitoring composable for tracking and optimizing app performance
import { ref, computed, onMounted, onUnmounted } from 'vue'

export const usePerformanceMonitor = () => {
  const metrics = ref({
    // Core Web Vitals
    fcp: null, // First Contentful Paint
    lcp: null, // Largest Contentful Paint
    fid: null, // First Input Delay
    cls: null, // Cumulative Layout Shift
    
    // Additional metrics
    ttfb: null, // Time to First Byte
    domContentLoaded: null,
    loadComplete: null,
    
    // Custom metrics
    animationFrameRate: null,
    memoryUsage: null,
    
    // Performance warnings
    warnings: []
  })

  const isMonitoring = ref(false)
  const performanceObserver = ref(null)
  const animationFrameId = ref(null)

  // Initialize performance monitoring
  const startMonitoring = () => {
    if (typeof window === 'undefined' || isMonitoring.value) return

    isMonitoring.value = true

    // Monitor Core Web Vitals
    monitorWebVitals()
    
    // Monitor animation performance
    monitorAnimationPerformance()
    
    // Monitor memory usage
    monitorMemoryUsage()
    
    // Monitor navigation timing
    monitorNavigationTiming()
  }

  // Monitor Core Web Vitals using PerformanceObserver
  const monitorWebVitals = () => {
    if (!('PerformanceObserver' in window)) return

    try {
      // First Contentful Paint
      const fcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint')
        if (fcpEntry) {
          metrics.value.fcp = Math.round(fcpEntry.startTime)
          checkPerformanceThresholds('fcp', metrics.value.fcp)
        }
      })
      fcpObserver.observe({ entryTypes: ['paint'] })

      // Largest Contentful Paint
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]
        if (lastEntry) {
          metrics.value.lcp = Math.round(lastEntry.startTime)
          checkPerformanceThresholds('lcp', metrics.value.lcp)
        }
      })
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })

      // First Input Delay
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach(entry => {
          if (entry.processingStart && entry.startTime) {
            metrics.value.fid = Math.round(entry.processingStart - entry.startTime)
            checkPerformanceThresholds('fid', metrics.value.fid)
          }
        })
      })
      fidObserver.observe({ entryTypes: ['first-input'] })

      // Cumulative Layout Shift
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0
        const entries = list.getEntries()
        entries.forEach(entry => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value
          }
        })
        metrics.value.cls = Math.round(clsValue * 1000) / 1000
        checkPerformanceThresholds('cls', metrics.value.cls)
      })
      clsObserver.observe({ entryTypes: ['layout-shift'] })

      performanceObserver.value = {
        fcp: fcpObserver,
        lcp: lcpObserver,
        fid: fidObserver,
        cls: clsObserver
      }
    } catch (error) {
      console.warn('Error setting up performance observers:', error)
    }
  }

  // Monitor animation frame rate
  const monitorAnimationPerformance = () => {
    let frameCount = 0
    let lastTime = performance.now()
    let fps = 60

    const measureFPS = (currentTime) => {
      frameCount++
      
      if (currentTime - lastTime >= 1000) {
        fps = Math.round((frameCount * 1000) / (currentTime - lastTime))
        metrics.value.animationFrameRate = fps
        
        // Check for performance issues
        if (fps < 30) {
          addWarning('Low frame rate detected', `Current FPS: ${fps}`)
        }
        
        frameCount = 0
        lastTime = currentTime
      }
      
      if (isMonitoring.value) {
        animationFrameId.value = requestAnimationFrame(measureFPS)
      }
    }

    animationFrameId.value = requestAnimationFrame(measureFPS)
  }

  // Monitor memory usage
  const monitorMemoryUsage = () => {
    if (!('memory' in performance)) return

    const updateMemoryMetrics = () => {
      try {
        const memory = performance.memory
        metrics.value.memoryUsage = {
          used: Math.round(memory.usedJSHeapSize / 1048576), // MB
          total: Math.round(memory.totalJSHeapSize / 1048576), // MB
          limit: Math.round(memory.jsHeapSizeLimit / 1048576) // MB
        }

        // Check for memory issues
        const usagePercentage = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
        if (usagePercentage > 80) {
          addWarning('High memory usage detected', `Memory usage: ${usagePercentage.toFixed(1)}%`)
        }
      } catch (error) {
        console.warn('Error reading memory metrics:', error)
      }
    }

    // Update memory metrics every 5 seconds
    const memoryInterval = setInterval(updateMemoryMetrics, 5000)
    updateMemoryMetrics() // Initial measurement

    // Store interval for cleanup
    performanceObserver.value = performanceObserver.value || {}
    performanceObserver.value.memoryInterval = memoryInterval
  }

  // Monitor navigation timing
  const monitorNavigationTiming = () => {
    if (!('navigation' in performance)) return

    try {
      const navigation = performance.getEntriesByType('navigation')[0]
      if (navigation) {
        metrics.value.ttfb = Math.round(navigation.responseStart - navigation.requestStart)
        metrics.value.domContentLoaded = Math.round(navigation.domContentLoadedEventEnd - navigation.navigationStart)
        metrics.value.loadComplete = Math.round(navigation.loadEventEnd - navigation.navigationStart)

        // Check thresholds
        checkPerformanceThresholds('ttfb', metrics.value.ttfb)
        checkPerformanceThresholds('domContentLoaded', metrics.value.domContentLoaded)
        checkPerformanceThresholds('loadComplete', metrics.value.loadComplete)
      }
    } catch (error) {
      console.warn('Error reading navigation timing:', error)
    }
  }

  // Check performance against thresholds and add warnings
  const checkPerformanceThresholds = (metric, value) => {
    const thresholds = {
      fcp: { good: 1800, poor: 3000 },
      lcp: { good: 2500, poor: 4000 },
      fid: { good: 100, poor: 300 },
      cls: { good: 0.1, poor: 0.25 },
      ttfb: { good: 800, poor: 1800 },
      domContentLoaded: { good: 2000, poor: 4000 },
      loadComplete: { good: 3000, poor: 6000 }
    }

    const threshold = thresholds[metric]
    if (!threshold) return

    if (value > threshold.poor) {
      addWarning(`Poor ${metric.toUpperCase()} performance`, `${metric}: ${value}ms (threshold: ${threshold.poor}ms)`)
    } else if (value > threshold.good) {
      addWarning(`Needs improvement ${metric.toUpperCase()}`, `${metric}: ${value}ms (threshold: ${threshold.good}ms)`)
    }
  }

  // Add performance warning
  const addWarning = (title, description) => {
    const warning = {
      id: Date.now(),
      title,
      description,
      timestamp: new Date().toISOString()
    }
    
    metrics.value.warnings.push(warning)
    
    // Limit warnings to last 10
    if (metrics.value.warnings.length > 10) {
      metrics.value.warnings = metrics.value.warnings.slice(-10)
    }

    // Log warning in development
    if (process.dev) {
      console.warn(`Performance Warning: ${title}`, description)
    }
  }

  // Stop monitoring
  const stopMonitoring = () => {
    isMonitoring.value = false

    // Disconnect performance observers
    if (performanceObserver.value) {
      Object.values(performanceObserver.value).forEach(observer => {
        if (observer && typeof observer.disconnect === 'function') {
          observer.disconnect()
        } else if (typeof observer === 'number') {
          clearInterval(observer)
        }
      })
      performanceObserver.value = null
    }

    // Cancel animation frame
    if (animationFrameId.value) {
      cancelAnimationFrame(animationFrameId.value)
      animationFrameId.value = null
    }
  }

  // Get performance score
  const getPerformanceScore = computed(() => {
    const scores = []
    
    // FCP score
    if (metrics.value.fcp !== null) {
      if (metrics.value.fcp <= 1800) scores.push(100)
      else if (metrics.value.fcp <= 3000) scores.push(50)
      else scores.push(0)
    }
    
    // LCP score
    if (metrics.value.lcp !== null) {
      if (metrics.value.lcp <= 2500) scores.push(100)
      else if (metrics.value.lcp <= 4000) scores.push(50)
      else scores.push(0)
    }
    
    // FID score
    if (metrics.value.fid !== null) {
      if (metrics.value.fid <= 100) scores.push(100)
      else if (metrics.value.fid <= 300) scores.push(50)
      else scores.push(0)
    }
    
    // CLS score
    if (metrics.value.cls !== null) {
      if (metrics.value.cls <= 0.1) scores.push(100)
      else if (metrics.value.cls <= 0.25) scores.push(50)
      else scores.push(0)
    }

    return scores.length > 0 ? Math.round(scores.reduce((a, b) => a + b, 0) / scores.length) : null
  })

  // Clear warnings
  const clearWarnings = () => {
    metrics.value.warnings = []
  }

  // Get performance report
  const getPerformanceReport = () => {
    return {
      metrics: { ...metrics.value },
      score: getPerformanceScore.value,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }
  }

  // Lifecycle hooks
  onMounted(() => {
    // Start monitoring after a short delay to avoid affecting initial load
    setTimeout(startMonitoring, 1000)
  })

  onUnmounted(() => {
    stopMonitoring()
  })

  return {
    metrics: computed(() => metrics.value),
    isMonitoring: computed(() => isMonitoring.value),
    performanceScore: getPerformanceScore,
    startMonitoring,
    stopMonitoring,
    clearWarnings,
    getPerformanceReport,
    addWarning
  }
}
