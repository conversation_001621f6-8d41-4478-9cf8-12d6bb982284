@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* Default CSS variables for both client and server rendering */
  :root {
    /* Main colors */
    --primary-color: #00FF41;    /* Matrix green */
    --secondary-color: #008F11;  /* Darker green */
    --accent-color: #003B00;     /* Very dark green */
    --background-color: #121212; /* Dark background */

    /* RGB values for rgba usage */
    --primary-rgb: 0, 255, 65;
    --secondary-rgb: 0, 143, 17;
    --accent-rgb: 0, 59, 0;

    /* Transition for smooth theme changes */
    --theme-transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
  }

  /* Web Dev Theme (Blue) */
  html.theme-web {
    /* Main colors */
    --primary-color: #00A3FF;    /* Bright blue */
    --secondary-color: #0077B6;  /* Medium blue */
    --accent-color: #003D5B;     /* Dark blue */

    /* RGB values for rgba usage */
    --primary-rgb: 0, 163, 255;
    --secondary-rgb: 0, 119, 182;
    --accent-rgb: 0, 61, 91;
  }

  body {
    /* Remove background-color from body to allow particles to show through */
    color: white;
  }
}

/* Theme-specific classes for consistent hydration */
.project-theme-gradient {
  /* Default game dev theme (green) */
  @apply bg-gradient-to-r from-green-400 to-green-600;
  background-clip: text;
  -webkit-background-clip: text;
  background: linear-gradient(to right, var(--primary-color, #00FF41), var(--secondary-color, #008F11));
  -webkit-text-fill-color: transparent;
}

.project-theme-text {
  /* Default game dev theme (green) */
  @apply text-green-400;
  color: var(--primary-color, #00FF41);
}

.project-theme-border {
  /* Default game dev theme (green) */
  @apply border-green-400;
  border-color: var(--primary-color, #00FF41);
}

/* Web theme variants */
html.theme-web .project-theme-gradient {
  @apply bg-gradient-to-r from-blue-400 to-blue-600;
  background: linear-gradient(to right, var(--primary-color, #00A3FF), var(--secondary-color, #0077B6));
}

html.theme-web .project-theme-text {
  @apply text-blue-400;
  color: var(--primary-color, #00A3FF);
}

html.theme-web .project-theme-border {
  @apply border-blue-400;
  border-color: var(--primary-color, #00A3FF);
}

/* Super fancy page transitions */
.page-enter-active {
  transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.page-leave-active {
  transition: all 0.4s cubic-bezier(0.36, 0, 0.66, -0.56);
}

.page-enter-from {
  opacity: 0;
  filter: blur(1rem);
  transform: scale(1.1) translateY(-10px);
}

.page-leave-to {
  opacity: 0;
  filter: blur(1rem);
  transform: scale(0.9) translateY(10px);
}

.prose {
  max-width: 75ch;
  color: white;
}

.prose h1 {
  color: var(--primary-color);
}

.prose h2 {
  color: var(--secondary-color);
}

.prose strong {
  color: var(--accent-color);
}