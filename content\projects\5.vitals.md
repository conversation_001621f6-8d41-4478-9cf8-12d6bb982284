---
title: 'Vitals: Health and Stamina'
description: Vitals is a robust health and stamina system. It offers a modular design, easy saving and loading, and streamlined UI binding for efficient game development.
coverImage: /images/projects/vitals.png
colors:
  primary: '#f6213e'
  secondary: '#d7bf07'
  accent: '#d7bf07'
  background: '#08203c'
techStack:
  - name: C#
    icon: devicon:csharp
  - name: Unity
    icon: bi:unity
  - name: Docusaurus
    icon: logos:docusaurus
  - name: GitHub
    icon: mdi:github
tags:
  - type: Individual
    icon: mdi:user
  - type: Unity Asset
    icon: mdi:unity
roles:
  - Programmer
info:
  table:
    - label: Duration
      value: 2 months
  buttons:
    - text: Asset Store
      url: https://assetstore.unity.com/packages/tools/utilities/vitals-health-and-stamina-system-247422
    - text: Documentation
      url: https://vitals-docs.netlify.app/
    - text: View Source
      url: https://github.com/FadiNahhas/UnityVitalsSystem/tree/main
---

# Vitals: Health and Stamina

Vitals is a health and stamina system for Unity. It offers a modular design, easy saving and loading, and streamlined UI binding for efficient game development.

## Features

- **Modular Design**: Different features are set up as separate components which can be easily added and removed. Different components communicate with each other using events.
- **Easy Data Binding**: Uses Scriptable Objects and events to communicate and efficiently update the UI.
- **Save and Load**: Simple saving and loading of any Vitals component data.
- **ScriptableObject Configuration**: Parameters are managed via ScriptableObjects, enabling easy, external configuration and iteration.

::gallery{items="/images/projects/vitals/1.png,/images/projects/vitals/2.png,/images/projects/vitals/3.png,/images/projects/vitals/4.png" alt="Defeat Waves of Enemies,Unlock New Items,Play and Earn Rewards,Upgrade Items"}
::