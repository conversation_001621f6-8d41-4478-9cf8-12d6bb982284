<template>
  <div class="contact-form">
    <h2 class="text-xl font-semibold mb-4 project-theme-text" :style="{ color: themeStore.themeColors.primary }">
      Send Me a Message
    </h2>

    <form ref="form" @submit.prevent="sendEmail" class="space-y-4">
      <!-- Two-column layout for name and email on larger screens -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Name Input -->
        <div class="form-group">
          <label for="name" class="block text-sm font-medium text-gray-300 mb-1">Name</label>
          <div class="relative">
            <input
              type="text"
              id="name"
              name="name"
              v-model="formData.name"
              required
              class="w-full px-3 py-2 bg-black/50 border rounded-md focus:outline-none transition-all duration-300"
              :class="{ 'border-red-500': errors.name }"
              :style="getInputStyle(errors.name)"
              @focus="handleFocus"
              @blur="validateField('name')"
            />
            <div
              class="input-highlight absolute bottom-0 left-0 h-0.5 transform scale-x-0 transition-transform duration-300 origin-left"
              :style="{ backgroundColor: themeStore.themeColors.primary }"
            ></div>
            <div class="third-wave"></div>
          </div>
          <p v-if="errors.name" class="text-red-500 text-xs mt-1">{{ errors.name }}</p>
        </div>

        <!-- Email Input -->
        <div class="form-group">
          <label for="email" class="block text-sm font-medium text-gray-300 mb-1">Email</label>
          <div class="relative">
            <input
              type="email"
              id="email"
              name="email"
              v-model="formData.email"
              required
              class="w-full px-3 py-2 bg-black/50 border rounded-md focus:outline-none transition-all duration-300"
              :class="{ 'border-red-500': errors.email }"
              :style="getInputStyle(errors.email)"
              @focus="handleFocus"
              @blur="validateField('email')"
            />
            <div
              class="input-highlight absolute bottom-0 left-0 h-0.5 transform scale-x-0 transition-transform duration-300 origin-left"
              :style="{ backgroundColor: themeStore.themeColors.primary }"
            ></div>
            <div class="third-wave"></div>
          </div>
          <p v-if="errors.email" class="text-red-500 text-xs mt-1">{{ errors.email }}</p>
        </div>
      </div>

      <!-- Subject Input -->
      <div class="form-group">
        <label for="subject" class="block text-sm font-medium text-gray-300 mb-1">Subject</label>
        <div class="relative">
          <input
            type="text"
            id="subject"
            name="subject"
            v-model="formData.subject"
            required
            class="w-full px-3 py-2 bg-black/50 border rounded-md focus:outline-none transition-all duration-300"
            :class="{ 'border-red-500': errors.subject }"
            :style="getInputStyle(errors.subject)"
            @focus="handleFocus"
            @blur="validateField('subject')"
          />
          <div
            class="input-highlight absolute bottom-0 left-0 h-0.5 transform scale-x-0 transition-transform duration-300 origin-left"
            :style="{ backgroundColor: themeStore.themeColors.primary }"
          ></div>
          <div class="third-wave"></div>
        </div>
        <p v-if="errors.subject" class="text-red-500 text-xs mt-1">{{ errors.subject }}</p>
      </div>

      <!-- Message Input -->
      <div class="form-group">
        <label for="message" class="block text-sm font-medium text-gray-300 mb-1">Message</label>
        <div class="relative">
          <div class="textarea-wrapper relative">
            <textarea
              id="message"
              name="message"
              v-model="formData.message"
              rows="4"
              required
              class="resize-none w-full px-3 py-2 bg-black/50 border rounded-md focus:outline-none transition-all duration-300"
              :class="{ 'border-red-500': errors.message }"
              :style="getInputStyle(errors.message)"
              @focus="handleFocus"
              @blur="validateField('message')"
            ></textarea>
            <div
              class="input-highlight absolute bottom-0 left-0 h-0.5 transform scale-x-0 transition-transform duration-300 origin-left"
              :style="{ backgroundColor: themeStore.themeColors.primary }"
            ></div>
          </div>
          <div class="third-wave"></div>
        </div>
        <p v-if="errors.message" class="text-red-500 text-xs mt-1">{{ errors.message }}</p>
      </div>

      <!-- Submit Button and Status Message in a row -->
      <div class="flex flex-col md:flex-row md:items-center gap-4">
        <div class="form-group">
          <button
            type="submit"
            :disabled="isSending"
            class="group relative inline-flex items-center justify-center px-5 py-2 overflow-hidden rounded-md text-white font-medium transition-all duration-300"
            :style="{
              backgroundColor: themeStore.isGameDev ? '#00FF41' : '#00A3FF',
              color: '#000000',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
            }"
            ref="submitButton"
          >
            <span class="relative z-10 flex items-center justify-center space-x-2">
              <span v-if="!isSending" class="text-black font-bold">Send Message</span>
              <span v-else class="text-black font-bold">Sending...</span>
              <Icon v-if="!isSending" icon="mdi:send" class="w-4 h-4 text-black transform transition-transform duration-500 group-hover:translate-x-1" />
              <Icon v-else icon="mdi:loading" class="w-4 h-4 text-black animate-spin" />
            </span>
          </button>
        </div>

        <!-- Status Message -->
        <div v-if="sendStatus" class="status-message text-center py-2 px-4 rounded-md flex-grow" :class="statusClass">
          {{ sendStatus }}
        </div>
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { gsap } from 'gsap';
import { Icon } from '@iconify/vue';
import { useThemeStore } from '~/stores/theme';

// Get the theme store
const themeStore = useThemeStore();

// Form reference
const form = ref(null);
const submitButton = ref(null);

// Form data
const formData = reactive({
  name: '',
  email: '',
  subject: '',
  message: ''
});

// Form validation errors
const errors = reactive({
  name: '',
  email: '',
  subject: '',
  message: ''
});

// Form state
const isSending = ref(false);
const sendStatus = ref('');

// Status message styling

// Status message class
const statusClass = computed(() => {
  if (sendStatus.value.includes('sent')) {
    return 'bg-green-900/50 text-green-300 border border-green-700';
  } else if (sendStatus.value.includes('Failed')) {
    return 'bg-red-900/50 text-red-300 border border-red-700';
  }
  return '';
});

// Function to get input style based on validation state
const getInputStyle = (hasError) => {
  const primary = themeStore.themeColors.primary || (themeStore.isGameDev ? '#00FF41' : '#00A3FF');

  return {
    borderColor: hasError ? '#ef4444' : 'rgba(75, 85, 99, 0.5)',
    '--focus-ring-color': primary
  };
};

// Handle input focus with simplified animation
const handleFocus = (event) => {
  const input = event.target;
  const isTextarea = input.tagName.toLowerCase() === 'textarea';

  // Get the highlight element - different for textarea vs regular inputs
  let highlight;
  if (isTextarea) {
    // For textarea, the highlight is inside the textarea-wrapper
    highlight = input.parentElement.querySelector('.input-highlight');
  } else {
    // For regular inputs, it's the next sibling
    highlight = input.nextElementSibling;
  }

  const primary = themeStore.themeColors.primary || (themeStore.isGameDev ? '#00FF41' : '#00A3FF');

  // Create a timeline for coordinated animations
  const tl = gsap.timeline();

  // Animate the highlight bar
  if (highlight) {
    // First reset to ensure animation plays every time
    gsap.set(highlight, {
      scaleX: 0,
      opacity: isTextarea ? 0.95 : 0.8 // Higher opacity for textarea
    });

    // Animate the bar
    tl.to(highlight, {
      scaleX: 1,
      duration: 0.5,
      ease: 'power2.out'
    });
  }

  // Animate the input border
  tl.to(input, {
    borderColor: primary,
    duration: 0.3,
    ease: 'power2.out'
  }, "-=0.3");
};

// Helper function to convert hex to RGB
const hexToRgb = (hex) => {
  // Remove # if present
  hex = hex.replace('#', '');

  // Handle shorthand hex
  if (hex.length === 3) {
    hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
  }

  // Parse hex values
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  return { r, g, b };
};

// Validate a specific field
const validateField = (field) => {
  // Reset the error for this field
  errors[field] = '';

  // Reset the highlight animation and input styling
  const input = document.getElementById(field);
  if (input) {
    // Check if it's a textarea
    const isTextarea = input.tagName.toLowerCase() === 'textarea';

    // Get the highlight element - different for textarea vs regular inputs
    let highlight;
    if (isTextarea) {
      // For textarea, the highlight is inside the textarea-wrapper
      highlight = input.parentElement.querySelector('.input-highlight');
    } else {
      // For regular inputs, it's the next sibling
      highlight = input.nextElementSibling;
    }

    // Reset the highlight bar
    if (highlight) {
      gsap.to(highlight, {
        scaleX: 0,
        duration: 0.3,
        ease: 'power2.in'
      });
    }

    // Reset the input border (unless there's an error)
    if (!errors[field]) {
      gsap.to(input, {
        borderColor: 'rgba(75, 85, 99, 0.5)',
        boxShadow: 'none',
        duration: 0.3
      });
    }
  }

  // Validate based on field type
  switch (field) {
    case 'name':
      if (!formData.name.trim()) {
        errors.name = 'Name is required';
      } else if (formData.name.trim().length < 2) {
        errors.name = 'Name must be at least 2 characters';
      }
      break;
    case 'email':
      if (!formData.email.trim()) {
        errors.email = 'Email is required';
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
        errors.email = 'Please enter a valid email address';
      }
      break;
    case 'subject':
      if (!formData.subject.trim()) {
        errors.subject = 'Subject is required';
      } else if (formData.subject.trim().length < 3) {
        errors.subject = 'Subject must be at least 3 characters';
      }
      break;
    case 'message':
      if (!formData.message.trim()) {
        errors.message = 'Message is required';
      } else if (formData.message.trim().length < 10) {
        errors.message = 'Message must be at least 10 characters';
      }
      break;
  }
};

// Validate all fields
const validateForm = () => {
  validateField('name');
  validateField('email');
  validateField('subject');
  validateField('message');

  // Return true if no errors
  return !errors.name && !errors.email && !errors.subject && !errors.message;
};

// Send email function
const sendEmail = async () => {
  // Validate form
  if (!validateForm()) {
    return;
  }

  // Set sending state
  isSending.value = true;
  sendStatus.value = '';

  try {
    // Animate button while sending
    gsap.to(submitButton.value, {
      scale: 0.98,
      duration: 0.2,
      repeat: -1,
      yoyo: true
    });

    // Call the server API endpoint to send the email
    const response = await fetch('/api/send-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(formData)
    });

    const result = await response.json();

    // Stop button animation
    gsap.killTweensOf(submitButton.value);
    gsap.to(submitButton.value, {
      scale: 1,
      duration: 0.3
    });

    // Handle response
    if (response.ok) {
      // Success
      sendStatus.value = 'Message sent!';

      // Reset form
      formData.name = '';
      formData.email = '';
      formData.subject = '';
      formData.message = '';

      // Clear status after delay
      setTimeout(() => {
        sendStatus.value = '';
      }, 5000);
    } else {
      // Error
      const errorMessage = result.data || result.error || 'Unknown error';

      // Special handling for configuration errors
      if (errorMessage === 'Missing EmailJS credentials') {
        sendStatus.value = 'Contact form is currently unavailable. Please email me <NAME_EMAIL>';

        // Keep this message visible longer
        setTimeout(() => {
          sendStatus.value = '';
        }, 10000);
      } else {
        sendStatus.value = 'Failed to send message: ' + errorMessage;

        // Clear status after delay
        setTimeout(() => {
          sendStatus.value = '';
        }, 5000);
      }
    }
  } catch (error) {
    console.error('Error sending email:', error);
    sendStatus.value = 'Failed to send message. Please try again later.';

    // Clear status after delay
    setTimeout(() => {
      sendStatus.value = '';
    }, 5000);
  } finally {
    isSending.value = false;
  }
};

// Set CSS variables based on theme
const setCssVariables = () => {
  const primary = themeStore.themeColors.primary || (themeStore.isGameDev ? '#00FF41' : '#00A3FF');
  const rgbValues = hexToRgb(primary);

  document.documentElement.style.setProperty('--primary-color', primary);
  document.documentElement.style.setProperty('--primary-rgb', `${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}`);

  // Add or remove web-dev class based on theme
  if (themeStore.isGameDev) {
    document.documentElement.classList.remove('web-dev');
  } else {
    document.documentElement.classList.add('web-dev');
  }
};

// Animations on mount
onMounted(() => {
  // Set CSS variables
  setCssVariables();

  // Watch for theme changes
  watch(() => themeStore.isGameDev, () => {
    setCssVariables();
  });

  // Watch for project theme changes
  watch(() => themeStore.themeColors, () => {
    setCssVariables();
  }, { deep: true });
  // Create a timeline for coordinated animations
  const tl = gsap.timeline();

  // Animate form title
  const title = document.querySelector('.contact-form h2');
  if (title) {
    tl.from(title, {
      y: -10,
      opacity: 0,
      duration: 0.4,
      ease: 'power2.out'
    });
  }

  // Animate name and email inputs (first row)
  const firstRowInputs = document.querySelectorAll('.grid.grid-cols-1 .form-group');
  tl.from(firstRowInputs, {
    x: -20,
    opacity: 0,
    stagger: 0.1,
    duration: 0.4,
    ease: 'power2.out'
  }, "-=0.2");

  // Animate subject and message inputs
  const otherInputs = document.querySelectorAll('.form-group:not(.grid .form-group)');
  tl.from(otherInputs, {
    y: 15,
    opacity: 0,
    stagger: 0.1,
    duration: 0.4,
    ease: 'power2.out'
  }, "-=0.2");

  // Make sure submit button is visible first
  if (submitButton.value) {
    gsap.set(submitButton.value, {
      opacity: 1,
      visibility: 'visible',
      display: 'inline-flex'
    });

    // Simple animation without opacity changes
    tl.from(submitButton.value, {
      y: 5,
      duration: 0.4,
      ease: 'power2.out',
      clearProps: 'all' // Important: clear all props after animation
    }, "-=0.2");

    // Add a subtle highlight effect
    setTimeout(() => {
      gsap.to(submitButton.value, {
        boxShadow: '0 0 15px rgba(0, 0, 0, 0.4)',
        duration: 0.5,
        yoyo: true,
        repeat: 1,
        onComplete: () => {
          // Ensure visibility after animation
          gsap.set(submitButton.value, {
            opacity: 1,
            visibility: 'visible',
            display: 'inline-flex'
          });
        }
      });
    }, 800);
  }
});
</script>

<style scoped>
/* Input field styling */
.form-group input, .form-group textarea {
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.form-group input:focus, .form-group textarea:focus {
  outline: none;
  border-color: var(--focus-ring-color);
}

/* Input highlight bar */
.input-highlight {
  height: 2px;
  background-color: var(--primary-color);
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
  z-index: 2;
  box-shadow: 0 0 8px var(--primary-color), 0 0 12px var(--primary-color);
  opacity: 0.8;
}

/* Textarea wrapper and highlight styling */
.textarea-wrapper {
  position: relative;
  width: 100%;
  display: block;
}

.textarea-wrapper textarea {
  width: 100%;
  display: block;
}

.textarea-wrapper .input-highlight {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  width: 100%;
  box-shadow: 0 0 10px var(--primary-color), 0 0 15px var(--primary-color); /* Enhanced glow */
  z-index: 3;
}

/* Input container */
.form-group .relative {
  position: relative;
  overflow: hidden;
  border-radius: 0.375rem;
}

/* Input container */
.form-group .relative {
  position: relative;
}

/* Third wave element - keeping this for compatibility but making it invisible */
.third-wave {
  display: none;
}

.status-message {
  transition: all 0.3s ease;
}

/* Force button visibility */
button[type="submit"] {
  opacity: 1 !important;
  visibility: visible !important;
  display: inline-flex !important;
  color: black !important;
  background-color: var(--primary-color) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

button[type="submit"] span {
  color: black !important;
  font-weight: bold !important;
}

button[type="submit"] svg {
  color: black !important;
}

/* Add CSS variables for theme colors */
:root {
  --primary-color: #00FF41;
  --primary-rgb: 0, 255, 65;
}

:root.web-dev {
  --primary-color: #00A3FF;
  --primary-rgb: 0, 163, 255;
}
</style>
