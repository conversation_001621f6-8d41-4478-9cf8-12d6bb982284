<template>
  <ol class="themed-ordered-list">
    <slot />
  </ol>
</template>

<script setup>
import { computed } from 'vue';
import { useThemeStore } from '~/stores/theme';

const themeStore = useThemeStore();

// Get the primary color from the theme store
const primaryColor = computed(() => themeStore.themeColors.primary);

// Convert hex to rgba
const hexToRgba = (hex, alpha = 1) => {
  if (!hex) return `rgba(0, 255, 65, ${alpha})`;
  
  // Remove # if present
  hex = hex.replace(/^#/, '');
  
  // Parse hex values
  let bigint = parseInt(hex, 16);
  let r = (bigint >> 16) & 255;
  let g = (bigint >> 8) & 255;
  let b = bigint & 255;
  
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

// Apply the theme color to the list markers
const markerColor = computed(() => hexToRgba(primaryColor.value, 1));
const markerGlow = computed(() => hexToRgba(primaryColor.value, 0.6));
</script>

<style scoped>
.themed-ordered-list {
  counter-reset: item;
  list-style: none;
  padding-left: 1.5rem;
  margin: 1.25rem 0;
  position: relative;
}

.themed-ordered-list :deep(li) {
  position: relative;
  padding-left: 0.5rem;
  margin-bottom: 0.75rem;
  line-height: 1.6;
  counter-increment: item;
}

.themed-ordered-list :deep(li::before) {
  content: counter(item);
  position: absolute;
  left: -1.75rem;
  top: 0.1rem;
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.3);
  color: v-bind('markerColor');
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 8px v-bind('markerGlow');
  border: 1px solid v-bind('hexToRgba(primaryColor, 0.3)');
}

/* Add a subtle connecting line between list items */
.themed-ordered-list :deep(li:not(:last-child)::after) {
  content: '';
  position: absolute;
  left: -1.125rem;
  top: 1.25rem;
  width: 1px;
  height: calc(100% - 0.5rem);
  background: linear-gradient(to bottom, 
    v-bind('hexToRgba(primaryColor, 0.6)') 0%,
    v-bind('hexToRgba(primaryColor, 0.1)') 100%);
}
</style>
