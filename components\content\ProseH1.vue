<template>
  <!-- Skip the first h1 heading since it's already shown in the hero section -->
  <h1 class="heading text-3xl font-bold mb-6 pb-2" :style="headingStyle">
    <div class="heading-content">
      <slot />
    </div>
  </h1>
</template>

<script setup>
import { computed, inject } from 'vue';
import { useThemeStore } from '~/stores/theme';

const props = defineProps({
  id: {
    type: String,
    default: ''
  }
});


// Get the theme store
const themeStore = useThemeStore();

// Convert hex to rgb values
const hexToRgb = (hex) => {
  // Default color if no hex is provided
  if (!hex) return { r: 0, g: 255, b: 65 }; // Default green color

  // Remove # if present
  hex = hex.replace(/^#/, '');

  // Parse hex values
  let bigint = parseInt(hex, 16);
  let r = (bigint >> 16) & 255;
  let g = (bigint >> 8) & 255;
  let b = bigint & 255;

  return { r, g, b };
};

// Get primary color from theme
const primaryColor = computed(() => themeStore.themeColors.primary || '#00FF41');

// Get RGB values from primary color
const rgbValues = computed(() => {
  return hexToRgb(primaryColor.value);
});

// Style for the heading text
const headingStyle = computed(() => {
  return {
    borderBottom: `1px solid rgba(${rgbValues.value.r}, ${rgbValues.value.g}, ${rgbValues.value.b}, 0.3)`,
    color: 'white',
    textShadow: `0 0 10px rgba(${rgbValues.value.r}, ${rgbValues.value.g}, ${rgbValues.value.b}, 0.5)`
  };
});
</script>

<style scoped>
.heading {
  position: relative;
}

.heading-content {
  position: relative;
  z-index: 1;
}
</style>
