<template>
  <div class="skill-item">
    <GlassContainer class="h-auto flex flex-col" padding="p-3 sm:p-4 md:p-3 lg:p-4">
      <div class="text-2xl sm:text-3xl md:text-2xl lg:text-3xl mb-1 flex justify-center">
        <IconifyIcon :icon="icon" width="36" height="36" class="sm:w-12 sm:h-12 md:w-10 md:h-10 lg:w-12 lg:h-12 xl:w-14 xl:h-14" />
      </div>
      <div class="font-semibold mt-auto text-center text-[10px] sm:text-sm md:text-xs lg:text-sm"
           :class="{'text-[9px] sm:text-xs md:text-[10px] lg:text-xs': name.length > 10}">
        {{ name }}
      </div>
    </GlassContainer>
  </div>
</template>

<script setup>
import { Icon as IconifyIcon } from '@iconify/vue';
import GlassContainer from './GlassContainer.vue';

defineProps({
  name: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    required: true
  }
});
</script>

<style scoped>
.skill-item {
  transition: all 0.3s ease;
  margin: 1px;
}

/* Only apply hover effects on non-touch devices */
@media (hover: hover) {
  /* Base hover effect */
  .skill-item:hover {
    transform: translateY(-3px) scale(1.03);
    z-index: 10;
  }

  /* Add a subtle shadow on hover */
  .skill-item:hover :deep(.glass-container) {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 10px rgba(var(--primary-rgb), 0.3);
  }

  /* Responsive hover effects for different screen sizes */
  @media (max-width: 639px) {
    .skill-item:hover {
      transform: translateY(-2px) scale(1.02);
    }
  }

  @media (min-width: 640px) and (max-width: 767px) {
    .skill-item:hover {
      transform: translateY(-2px) scale(1.02);
    }
  }

  @media (min-width: 768px) {
    .skill-item:hover {
      transform: translateY(-3px) scale(1.03);
    }
  }
}

/* Mobile styles */
@media (max-width: 639px) {
  .skill-item {
    margin: 2px;
  }

  .skill-item > div {
    padding: 0.75rem !important;
  }
}

/* Tablet styles */
@media (min-width: 640px) and (max-width: 767px) {
  .skill-item {
    margin: 3px;
  }

  .skill-item > div {
    padding: 1rem !important;
  }
}

/* Desktop styles */
@media (min-width: 768px) {
  .skill-item {
    margin: 3px;
  }
}

/* Large desktop styles */
@media (min-width: 1280px) {
  .skill-item {
    margin: 4px;
  }
}
</style>
