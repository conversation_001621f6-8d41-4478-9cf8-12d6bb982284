---
title: WeTeach Website
description: A Vue and Firebase tutoring platform for We Teach, connecting
  tutors and students with seamless session scheduling and booking.
coverImage: /images/projects/weteach.png
colors:
  primary: "#24bba1"
  secondary: "#ffffff"
  accent: "#f38c49"
  background: "#003626"
techStack:
  - name: Vue.js
    icon: logos:vue
  - name: Vite
    icon: devicon:vitejs
  - name: Firebase
    icon: logos:firebase
  - name: Tailwind
    icon: logos:tailwindcss-icon
  - name: TypeScript
    icon: devicon:typescript
  - name: PrimeVue
    icon: simple-icons:primevue
  - name: Netlify
    icon: devicon:netlify
  - name: GitHub
    icon: mdi:github
  - name: Trello
    icon: devicon:trello
tags:
  - type: Individual
    icon: mdi:account
  - type: Client Project
    icon: mdi:handshake
roles:
  - Full-Stack Developer
  - Designer
info:
  table:
    - label: Duration
      value: 2 months
    - label: Platform
      value: Web, PWA
  buttons:
    - text: Visit Website
      url: https://we-teach.site
seo:
  title: WeTeach Website | Fadi Nahhas
  description: A Vue and Firebase tutoring platform for We Teach, connecting
    tutors and students with seamless session scheduling and booking.
navigation:
  title: WeTeach Website | Fadi Nahhas
---

# WeTeach Website

A comprehensive tutoring platform built with Vue and Firebase that enables tutors to schedule sessions and students to book them. This website serves as the digital hub for We Teach, a tutoring organization connecting qualified instructors with students seeking academic support.

::gallery
---
alt: Authentication Page,Schedule Page,Store Page,Store Page - Dark Mode,Admin
  Portal - Dark Mode
items: /images/projects/weteach/1.png,/images/projects/weteach/2.png,/images/projects/weteach/3.png,/images/projects/weteach/4.png,/images/projects/weteach/5.png
---
::

## Project Overview

WeTeach is a tutoring organization that needed a digital platform to streamline their operations. The website allows tutors to schedule classes, students to sign up for classes, and administrators to oversee and manage the entire process.

## Key Features

- **User Authentication**: Secure login system for students, tutors, and administrators
- **Session Scheduling**: Tutors can schedule classes for specific subjects, and students can sign up for classes
- **Credit System**: Students can purchase different types of credits to sign up for different types of classes
- **Payment Processing**: Integration with payment gateway for credit purchases
- **Notifications**: Push notifications for session reminders
- **Admin Dashboard**: Comprehensive dashboard for administrators to manage users, classes, economy, and more

### Frontend Development

The frontend was built using Vue.js with TypeScript for type safety. PrimeVue was used for UI components, providing a consistent and professional look and feel. The application was designed to be responsive, ensuring a seamless experience across all devices.

### Backend Development

Firebase was chosen as the backend solution for its real-time database capabilities, authentication system, and cloud functions. This allowed for:

- Real-time updates across the platform
- Secure user authentication
- Serverless functions for business logic
- Cloud storage for user files and resources

### Payment Processing

EasyCard was integrated for payment processing, allowing students to purchase credits securely. Netlify Functions were used to handle payment webhooks, which then triggered Firebase Functions to update user credit balances.
