<template>
  <div
    v-motion-slide-visible-once-bottom
    class="project-card bg-gray-950 rounded-xl shadow-xl overflow-hidden group h-[550px]"
    :style="{ borderColor: project.meta.colors?.primary }"
    ref="cardElement"
  >
    <NuxtLink :to="project.path" class="h-full flex flex-col" prefetch>
      <!-- Cover Image with Gradient Overlay -->
      <div class="relative h-48 md:h-56 overflow-hidden flex-shrink-0">
        <div class="absolute inset-0 bg-gradient-to-b from-transparent to-gray-900 opacity-70 z-10"></div>
        <img
          v-if="project.meta.coverImage"
          :src="project.meta.coverImage"
          :alt="project.title"
          loading="lazy"
          class="w-full h-full object-cover transition-transform duration-700 ease-out group-hover:scale-110"
        />
        <div v-else class="w-full h-full bg-gradient-to-br"
             :style="{
               backgroundImage: `linear-gradient(to bottom right, ${project.meta.colors?.primary || '#42b883'}, ${project.meta.colors?.secondary || '#347474'})`
             }"
        ></div>
        <!-- Project Tags (top right) -->
        <div v-if="project.meta.tags && project.meta.tags.length" class="absolute top-0 right-0 p-3 z-20 flex flex-col items-end space-y-2">
          <div v-for="tag in project.meta.tags" :key="tag.type"
               class="project-tag flex items-center px-2 py-1 rounded-md"
               :style="{ backgroundColor: `rgba(0, 0, 0, 0.7)`, borderLeft: `3px solid rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.8)` }">
            <Icon :icon="tag.icon" class="w-3.5 h-3.5 mr-1.5" />
            <span class="text-xs font-medium text-white">{{ tag.type }}</span>
          </div>
        </div>
        <!-- Project Title Overlay -->
        <div class="absolute bottom-0 left-0 right-0 p-4 z-20">
          <h2 class="text-xl md:text-2xl font-bold mb-1 text-white project-title">
            {{ project.title }}
          </h2>
        </div>
      </div>

      <!-- Project Description -->
      <div class="p-4 md:p-6 description-area"
           :style="{
             backgroundColor: `rgba(0, 0, 0, 0.7)`,
             borderTop: `2px solid rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.5)`
           }">
        <!-- Top content section -->
        <div>
          <!-- Description with fixed height and ellipsis for overflow -->
          <div class="description-text">
            <p class="text-sm md:text-base text-gray-300 mb-4">{{ project.description }}</p>
          </div>

          <!-- Roles Section -->
          <div v-if="project.meta.roles && project.meta.roles.length" class="mb-4 roles-section">
            <h3 class="text-xs uppercase tracking-wider mb-2 text-gray-400 font-medium">
              {{ project.meta.roles.length > 1 ? 'My Roles' : 'My Role' }}
            </h3>
            <div class="flex flex-wrap gap-2">
              <div v-for="role in project.meta.roles" :key="role"
                  class="role-badge"
                  :style="{
                    borderColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.6)`,
                    backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.2)`
                  }">
                <Icon icon="mdi:account-cog" class="w-3.5 h-3.5 mr-1.5" />
                <span class="text-xs font-medium">{{ role }}</span>
              </div>
            </div>
          </div>

          <!-- Tech Stack -->
          <div v-if="project.meta.techStack && project.meta.techStack.length" class="tech-stack">
            <h3 class="text-xs uppercase tracking-wider mb-2 text-gray-400 font-medium">Tech Stack</h3>
            <div class="flex flex-wrap gap-2">
              <div v-for="tech in project.meta.techStack" :key="tech.name"
                  class="tech-item"
                  :style="{
                    borderColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.6)`,
                    backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.15)`
                  }">
                <Icon :icon="tech.icon" class="w-4 h-4 mr-1.5" />
                <span class="text-xs font-medium">{{ tech.name }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- View Project Button - Always at the bottom -->
        <div class="flex justify-end mt-auto pt-4">
          <span class="inline-flex items-center px-3 py-1.5 rounded text-sm font-medium transition-all duration-300 view-project"
                :style="{
                  color: 'white',
                  backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.8)`,
                  boxShadow: `0 2px 4px rgba(0, 0, 0, 0.2)`
                }">
            View Project
            <Icon icon="mdi:arrow-right" class="ml-1 w-4 h-4 transform transition-transform duration-300 group-hover:translate-x-1" />
          </span>
        </div>
      </div>
    </NuxtLink>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { gsap } from 'gsap';
import { Icon } from '@iconify/vue';

const props = defineProps({
  project: {
    type: Object,
    required: true
  }
});

const cardElement = ref(null);

// Convert hex color to RGB values
const hexToRgb = (hex) => {
  // Default color if no hex is provided
  if (!hex) return { r: 66, g: 184, b: 131 }; // Default green color

  // Remove # if present
  hex = hex.replace(/^#/, '');

  // Parse hex values
  let bigint = parseInt(hex, 16);
  let r = (bigint >> 16) & 255;
  let g = (bigint >> 8) & 255;
  let b = bigint & 255;

  return { r, g, b };
};

// Compute RGB values from project primary color
const rgbValues = computed(() => {
  const primaryColor = props.project.meta.colors?.primary || '#42b883';
  // Remove # if present and ensure it's a valid hex color
  const cleanHex = primaryColor.replace(/^#/, '');
  return hexToRgb(cleanHex);
});

// Initialize fancy hover effects with GSAP
onMounted(() => {
  if (!cardElement.value) return;

  const card = cardElement.value;
  const title = card.querySelector('.project-title');
  const viewProject = card.querySelector('.view-project');

  // Create hover animations
  card.addEventListener('mouseenter', () => {
    // Animate title
    gsap.to(title, {
      y: -5,
      duration: 0.3,
      ease: 'power2.out'
    });

    // Animate view project button
    gsap.to(viewProject, {
      y: -2,
      boxShadow: `0 4px 8px rgba(0, 0, 0, 0.3)`,
      duration: 0.3,
      ease: 'power2.out'
    });

    // Add subtle glow to card
    gsap.to(card, {
      boxShadow: `0 10px 25px rgba(0, 0, 0, 0.3), 0 0 10px rgba(${rgbValues.value.r}, ${rgbValues.value.g}, ${rgbValues.value.b}, 0.2)`,
      duration: 0.4
    });
  });

  // Reset animations on mouse leave
  card.addEventListener('mouseleave', () => {
    // Reset title
    gsap.to(title, {
      y: 0,
      duration: 0.3,
      ease: 'power2.out'
    });

    // Reset view project button
    gsap.to(viewProject, {
      y: 0,
      boxShadow: `0 2px 4px rgba(0, 0, 0, 0.2)`,
      duration: 0.3,
      ease: 'power2.out'
    });

    // Reset card glow
    gsap.to(card, {
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
      duration: 0.4
    });
  });
});
</script>

<style scoped>
.project-card {
  border: 2px solid transparent;
  transition: transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1),
              box-shadow 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.project-card:hover {
  transform: translateY(-8px) scale(1.02);
}

.view-project {
  position: relative;
  transition: all 0.3s ease;
}

.project-card:hover .view-project {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
}

.description-area {
  transition: background-color 0.3s ease, border-top 0.3s ease;
  display: flex;
  flex-direction: column;
  flex: 1;
  justify-content: space-between; /* Push content to top and button to bottom */
}

.description-text {
  max-height: 80px; /* Limit description height */
  overflow: hidden;
  position: relative;
}

/* Add a fade effect at the bottom of truncated descriptions */
.description-text::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 20px;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.7));
  pointer-events: none;
}

.tech-stack {
  margin-top: 12px;
}

.tech-item {
  display: inline-flex;
  align-items: center;
  padding: 3px 8px;
  border-radius: 4px;
  border: 1px solid;
  transition: all 0.2s ease;
  color: rgba(255, 255, 255, 0.9);
}

.tech-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  color: white;
}

.project-tag {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.project-tag:hover {
  transform: translateX(-3px);
}

.role-badge {
  display: inline-flex;
  align-items: center;
  padding: 3px 8px;
  border-radius: 12px;
  border: 1px solid;
  transition: all 0.2s ease;
  color: rgba(255, 255, 255, 0.9);
}

.role-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  color: white;
}
</style>
