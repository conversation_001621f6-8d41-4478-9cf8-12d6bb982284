---
title: LUMI
description: LUMI is a 2D platformer game developed in Unity. You play as a light bulb that survived an earthquake, and you have to find your way out of the house. The player can control light and darkness, use them to cross obstacles.
coverImage: /images/projects/lumi.png
colors:
  primary: '#ffdd55'
  secondary: '#4c4637'
  accent: '#9f9065'
  background: '#333333'
techStack:
  - name: C#
    icon: devicon:csharp
  - name: Unity
    icon: bi:unity
  - name: GitHub
    icon: mdi:github
  - name: Jira
    icon: simple-icons:jira
  - name: Confluence
    icon: simple-icons:confluence
tags:
  - type: Group
    icon: mdi:account-group
  - type: University Project
    icon: mdi:school
roles:
  - Lead Programmer
  - Game Designer
info:
  table:
    - label: Duration
      value: 2 months
    - label: Platform
      value: Windows
  buttons:
    - text: View on itch.io
      url: https://fadinahhas.itch.io/lumi
---
# LUMI
LUMI is a 2D platformer game developed in Unity. You play as a light bulb that survived an earthquake, and you have to find your way out of the house. The player can control light and darkness, use them to cross obstacles. There are shadow hands around the house that will chase you when the light is on, BE CAREFUL!

## Trailer

::youtube{id="Sy0DWHWN6_4" title="LUMI Gameplay" aspectRatio="16:9"}
::

## Game Concept

LUMI is a puzzle platformer where you control a sentient light bulb trying to escape a house after an earthquake. The game explores themes of light and darkness, with unique mechanics that allow the player to manipulate their environment and cross obstacles.

## Gameplay Features

- **Light Control**: Toggle between light and dark for different abilities
- **Physics Puzzles**: Use abilities and platforms to navigate the environment
- **AI Shadows**: Avoid the shadow hands that chase you, use your abilities to lead them away from you

## Contribution
### Programming

- **Enemy AI**: Developed the behavior for the shadow hand enemies, implementing their light-sensitive chasing mechanics and pathfinding within the 2D environments
- **Character Controller**: Programmed the 2D player movement, jumping, and environmental interactions
- **Animation Programming**: Integrated character and enemy animations with game logic and state, ensuring seamless visual feedback for movement, actions, and AI states
- **UI Programming**: Implemented the game's user interface elements and menus

### Design & Art

- **Level Design**: Co-designed the level with the team, integrating puzzles, obstacles, enemy placement, and ligh/dark puzzles
- **Lighting**: Designed and implemented the atmospheric lighting scheme crucial to the core mechanics, including setting up dynamic lights and baking static lighting to enhance mood and performance
- **Physics**: Configured and utilized Unity's 2D physics system for character movement, object hitboxes, object interactions, and ensuring consistent behavior of game elements 
