// Optimized lazy component loading composable for better performance
import { ref, onMounted, onUnmounted, nextTick } from 'vue'

// Component cache for better performance
const componentCache = new Map()
const loadingStates = new Map()

export const useLazyComponent = (componentPath, options = {}) => {
  const {
    threshold = 0.1, // Intersection threshold
    rootMargin = '50px', // Load components 50px before they come into view
    immediate = false, // Load immediately without intersection observer
    cache = true, // Enable caching
    retryAttempts = 3, // Number of retry attempts on failure
    retryDelay = 1000 // Delay between retries
  } = options

  const component = ref(null)
  const isLoading = ref(false)
  const error = ref(null)
  const elementRef = ref(null)
  
  let observer = null
  let retryCount = 0

  // Check if component is already cached
  const getCachedComponent = () => {
    if (cache && componentCache.has(componentPath)) {
      return componentCache.get(componentPath)
    }
    return null
  }

  // Load component with retry logic
  const loadComponent = async () => {
    // Check if already loading
    if (loadingStates.get(componentPath)) {
      return loadingStates.get(componentPath)
    }

    // Check cache first
    const cached = getCachedComponent()
    if (cached) {
      component.value = cached
      return cached
    }

    isLoading.value = true
    error.value = null

    // Create loading promise
    const loadingPromise = (async () => {
      try {
        // Dynamic import with retry logic
        let importedComponent = null
        
        for (let attempt = 0; attempt <= retryAttempts; attempt++) {
          try {
            // Use dynamic import based on component path
            if (componentPath.startsWith('~/')) {
              // Handle Nuxt-style paths
              const modulePath = componentPath.replace('~/', './')
              importedComponent = await import(modulePath)
            } else {
              // Handle regular paths
              importedComponent = await import(componentPath)
            }
            break
          } catch (importError) {
            if (attempt === retryAttempts) {
              throw importError
            }
            
            // Wait before retry
            await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1)))
            retryCount++
          }
        }

        // Extract default export or use the module itself
        const resolvedComponent = importedComponent.default || importedComponent

        // Cache the component if caching is enabled
        if (cache) {
          componentCache.set(componentPath, resolvedComponent)
        }

        component.value = resolvedComponent
        return resolvedComponent
      } catch (loadError) {
        error.value = loadError
        console.error(`Failed to load component ${componentPath}:`, loadError)
        throw loadError
      } finally {
        isLoading.value = false
        loadingStates.delete(componentPath)
      }
    })()

    // Store loading promise to prevent duplicate requests
    loadingStates.set(componentPath, loadingPromise)
    
    return loadingPromise
  }

  // Setup intersection observer for lazy loading
  const setupObserver = () => {
    if (typeof window === 'undefined' || !elementRef.value) return

    observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            loadComponent()
            // Disconnect observer after loading
            if (observer) {
              observer.disconnect()
              observer = null
            }
          }
        })
      },
      {
        threshold,
        rootMargin
      }
    )

    observer.observe(elementRef.value)
  }

  // Initialize component loading
  const init = async () => {
    if (immediate) {
      // Load immediately
      await loadComponent()
    } else {
      // Wait for next tick to ensure element is mounted
      await nextTick()
      setupObserver()
    }
  }

  // Cleanup function
  const cleanup = () => {
    if (observer) {
      observer.disconnect()
      observer = null
    }
  }

  // Preload component (useful for critical components)
  const preload = () => {
    return loadComponent()
  }

  // Force reload component (bypass cache)
  const reload = async () => {
    if (cache) {
      componentCache.delete(componentPath)
    }
    loadingStates.delete(componentPath)
    component.value = null
    error.value = null
    retryCount = 0
    
    return loadComponent()
  }

  // Lifecycle hooks
  onMounted(() => {
    init()
  })

  onUnmounted(() => {
    cleanup()
  })

  return {
    component,
    isLoading,
    error,
    elementRef,
    loadComponent,
    preload,
    reload,
    retryCount
  }
}

// Utility function to preload multiple components
export const preloadComponents = async (componentPaths) => {
  const promises = componentPaths.map(path => {
    const { preload } = useLazyComponent(path, { immediate: false })
    return preload()
  })
  
  return Promise.allSettled(promises)
}

// Utility function to clear component cache
export const clearComponentCache = (componentPath = null) => {
  if (componentPath) {
    componentCache.delete(componentPath)
    loadingStates.delete(componentPath)
  } else {
    componentCache.clear()
    loadingStates.clear()
  }
}

// Get cache statistics for debugging
export const getCacheStats = () => {
  return {
    cachedComponents: componentCache.size,
    loadingComponents: loadingStates.size,
    cacheKeys: Array.from(componentCache.keys())
  }
}

// Optimized lazy image loading composable
export const useLazyImage = (imageSrc, options = {}) => {
  const {
    placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB2aWV3Qm94PSIwIDAgMSAxIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9IiNmMGYwZjAiLz48L3N2Zz4=',
    threshold = 0.1,
    rootMargin = '50px'
  } = options

  const imageRef = ref(null)
  const isLoaded = ref(false)
  const isLoading = ref(false)
  const error = ref(null)
  const currentSrc = ref(placeholder)

  let observer = null

  const loadImage = () => {
    if (isLoading.value || isLoaded.value) return

    isLoading.value = true
    error.value = null

    const img = new Image()
    
    img.onload = () => {
      currentSrc.value = imageSrc
      isLoaded.value = true
      isLoading.value = false
      
      // Disconnect observer after loading
      if (observer) {
        observer.disconnect()
        observer = null
      }
    }

    img.onerror = (err) => {
      error.value = err
      isLoading.value = false
    }

    img.src = imageSrc
  }

  const setupImageObserver = () => {
    if (typeof window === 'undefined' || !imageRef.value) return

    observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            loadImage()
          }
        })
      },
      { threshold, rootMargin }
    )

    observer.observe(imageRef.value)
  }

  onMounted(() => {
    nextTick(() => {
      setupImageObserver()
    })
  })

  onUnmounted(() => {
    if (observer) {
      observer.disconnect()
      observer = null
    }
  })

  return {
    imageRef,
    currentSrc,
    isLoaded,
    isLoading,
    error,
    loadImage
  }
}
