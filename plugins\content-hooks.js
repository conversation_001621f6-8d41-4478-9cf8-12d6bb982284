// Plugin to extend Nuxt Content with custom components for media and embeds
export default defineNuxtPlugin((nuxtApp) => {
  // Add custom parsers to the Nuxt Content module
  nuxtApp.hook('content:file:beforeParse', (file) => {
    if (file._id.endsWith('.md')) {
      // Process carousel shortcode: [carousel items="item1.jpg,item2.jpg,item3.jpg"]
      file.body = processCarouselShortcode(file.body);

      // Process YouTube embed shortcode: [youtube id="VIDEO_ID" title="Optional Title"]
      file.body = processYoutubeShortcode(file.body);

      // Process gallery shortcode: [gallery items="image1.jpg,image2.jpg" alt="Alt text 1,Alt text 2"]
      file.body = processGalleryShortcode(file.body);
    }
    return file;
  });
});

/**
 * Process carousel shortcode in markdown content
 * Format: [carousel items="item1.jpg,item2.jpg,item3.jpg" autoplay="true" interval="5000"]
 */
function processCarouselShortcode(content) {
  const carouselRegex = /\[carousel\s+([^\]]+)\]/g;

  return content.replace(carouselRegex, (match, attributesStr) => {
    try {
      // Parse attributes
      const attributes = parseAttributes(attributesStr);

      // Get items array
      const items = attributes.items ? attributes.items.split(',').map(item => item.trim()) : [];
      if (!items.length) return match; // Return original if no items

      // Convert items to JSON array of objects
      const itemsJson = items.map(item => {
        // Check if item is a video by extension
        const isVideo = item.endsWith('.mp4') || item.endsWith('.webm') || item.endsWith('.mov');
        return {
          type: isVideo ? 'video' : 'image',
          src: item,
          alt: attributes.alt || '' // Allow alt text to be specified
        };
      });

      // Build component HTML with proper escaping for JSON
      // Use HTML component syntax that Nuxt Content can parse
      const safeJsonString = JSON.stringify(itemsJson).replace(/"/g, '\\"');
      return `<div class="media-carousel-wrapper">
  <component :is="'MediaCarousel'" :items='${safeJsonString}' :autoplay="${attributes.autoplay || 'false'}" :interval="${attributes.interval || '5000'}" :showControls="${attributes.controls || 'true'}" :showDots="${attributes.dots || 'true'}"></component>
</div>`;
    } catch (error) {
      // Silently fail and return original content
      return match;
    }
  });
}

/**
 * Process YouTube embed shortcode in markdown content
 * Format: [youtube id="VIDEO_ID" title="Optional Title" description="Optional description"]
 */
function processYoutubeShortcode(content) {
  const youtubeRegex = /\[youtube\s+([^\]]+)\]/g;

  return content.replace(youtubeRegex, (match, attributesStr) => {
    try {
      // Parse attributes
      const attributes = parseAttributes(attributesStr);

      // Ensure we have a video ID
      if (!attributes.id) return match;

      // Escape any quotes in the title and description to prevent HTML issues
      const safeTitle = (attributes.title || '').replace(/"/g, '&quot;');
      const safeDescription = (attributes.description || '').replace(/"/g, '&quot;');

      // Build component using the double colon syntax for Nuxt Content
      return `::youtube{id="${attributes.id}" title="${safeTitle}" description="${safeDescription}" ${attributes.autoplay ? 'autoplay' : ''} ${attributes.overlay === 'false' ? ':showOverlay="false"' : ''} aspectRatio="${attributes.ratio || '16:9'}"}
::`;
    } catch (error) {
      // Silently fail and return original content
      return match;
    }
  });
}

/**
 * Process gallery shortcode in markdown content
 * Format: [gallery items="image1.jpg,image2.jpg,image3.jpg" alt="Alt text 1,Alt text 2,Alt text 3"]
 */
function processGalleryShortcode(content) {
  const galleryRegex = /\[gallery\s+([^\]]+)\]/g;

  return content.replace(galleryRegex, (match, attributesStr) => {
    try {
      // Parse attributes
      const attributes = parseAttributes(attributesStr);

      // Get items array
      const items = attributes.items ? attributes.items.split(',').map(item => item.trim()) : [];
      if (!items.length) return match; // Return original if no items

      // Get alt texts if provided
      const altTexts = attributes.alt ? attributes.alt.split(',').map(alt => alt.trim()) : [];

      // Convert items to JSON array of objects for carousel
      const itemsJson = items.map((item, index) => {
        // Check if item is a video by extension
        const isVideo = item.endsWith('.mp4') || item.endsWith('.webm') || item.endsWith('.mov');
        return {
          type: isVideo ? 'video' : 'image',
          src: item,
          alt: altTexts[index] || `Gallery image ${index + 1}`
        };
      });

      // Build component HTML with proper escaping for JSON
      // Use HTML component syntax that Nuxt Content can parse
      const safeJsonString = JSON.stringify(itemsJson).replace(/"/g, '\\"');
      return `<div class="media-carousel-wrapper">
  <component :is="'MediaCarousel'" :items='${safeJsonString}' :autoplay="false" :interval="5000" :showControls="true" :showDots="true"></component>
</div>`;
    } catch (error) {
      // Silently fail and return original content
      return match;
    }
  });
}

/**
 * Parse attribute string into an object
 * Handles both quoted and unquoted attribute values
 */
function parseAttributes(attributesStr) {
  const attributes = {};

  // Match attributes in format: name="value" or name=value
  const attrRegex = /(\w+)=(?:"([^"]*)"|'([^']*)'|([^\s]*))/g;
  let match;

  while ((match = attrRegex.exec(attributesStr)) !== null) {
    const name = match[1];
    // Get the value from whichever capturing group matched (quoted or unquoted)
    const value = match[2] || match[3] || match[4] || '';
    attributes[name] = value;
  }

  return attributes;
}
